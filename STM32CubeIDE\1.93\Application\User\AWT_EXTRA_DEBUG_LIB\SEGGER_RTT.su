SEGGER_RTT.c:312:13:_DoInit	16	static,ignoring_inline_asm
SEGGER_RTT.c:371:17:_WriteBlocking	48	static,ignoring_inline_asm
SEGGER_RTT.c:433:13:_WriteNoCheck	40	static,ignoring_inline_asm
SEGGER_RTT.c:501:13:_PostTerminalSwitch	24	static
SEGGER_RTT.c:523:17:_GetAvailWriteSpace	32	static
SEGGER_RTT.c:570:10:SEGGER_RTT_ReadUpBufferNoLock	56	static
SEGGER_RTT.c:662:10:SEGGER_RTT_ReadNoLock	56	static
SEGGER_RTT.c:759:10:SEGGER_RTT_ReadUpBuffer	32	static,ignoring_inline_asm
SEGGER_RTT.c:791:10:SEGGER_RTT_Read	32	static,ignoring_inline_asm
SEGGER_RTT.c:830:6:SEGGER_RTT_WriteWithOverwriteNoLock	40	static,ignoring_inline_asm
SEGGER_RTT.c:1021:10:SEGGER_RTT_WriteDownBufferNoLock	40	static
SEGGER_RTT.c:1096:10:SEG<PERSON>R_RTT_WriteNoLock	40	static
SEGGER_RTT.c:1172:10:SEGGER_RTT_WriteDownBuffer	40	static,ignoring_inline_asm
SEGGER_RTT.c:1201:10:SEGGER_RTT_Write	40	static,ignoring_inline_asm
SEGGER_RTT.c:1231:10:SEGGER_RTT_WriteString	24	static
SEGGER_RTT.c:1261:10:SEGGER_RTT_PutCharSkipNoLock	32	static,ignoring_inline_asm
SEGGER_RTT.c:1311:10:SEGGER_RTT_PutCharSkip	40	static,ignoring_inline_asm
SEGGER_RTT.c:1370:10:SEGGER_RTT_PutChar	40	static,ignoring_inline_asm
SEGGER_RTT.c:1433:5:SEGGER_RTT_GetKey	16	static
SEGGER_RTT.c:1461:5:SEGGER_RTT_WaitKey	16	static
SEGGER_RTT.c:1484:5:SEGGER_RTT_HasKey	24	static
SEGGER_RTT.c:1512:10:SEGGER_RTT_HasData	24	static
SEGGER_RTT.c:1533:10:SEGGER_RTT_HasDataUp	24	static
SEGGER_RTT.c:1561:5:SEGGER_RTT_AllocDownBuffer	40	static,ignoring_inline_asm
SEGGER_RTT.c:1609:5:SEGGER_RTT_AllocUpBuffer	40	static,ignoring_inline_asm
SEGGER_RTT.c:1663:5:SEGGER_RTT_ConfigUpBuffer	48	static,ignoring_inline_asm
SEGGER_RTT.c:1714:5:SEGGER_RTT_ConfigDownBuffer	48	static,ignoring_inline_asm
SEGGER_RTT.c:1757:5:SEGGER_RTT_SetNameUpBuffer	40	static,ignoring_inline_asm
SEGGER_RTT.c:1792:5:SEGGER_RTT_SetNameDownBuffer	40	static,ignoring_inline_asm
SEGGER_RTT.c:1827:5:SEGGER_RTT_SetFlagsUpBuffer	40	static,ignoring_inline_asm
SEGGER_RTT.c:1862:5:SEGGER_RTT_SetFlagsDownBuffer	40	static,ignoring_inline_asm
SEGGER_RTT.c:1890:6:SEGGER_RTT_Init	8	static
SEGGER_RTT.c:1911:5:SEGGER_RTT_SetTerminal	40	static,ignoring_inline_asm
SEGGER_RTT.c:1960:5:SEGGER_RTT_TerminalOut	40	static,ignoring_inline_asm
SEGGER_RTT.c:2048:10:SEGGER_RTT_GetAvailWriteSpace	24	static
SEGGER_RTT.c:2069:10:SEGGER_RTT_GetBytesInBuffer	32	static
