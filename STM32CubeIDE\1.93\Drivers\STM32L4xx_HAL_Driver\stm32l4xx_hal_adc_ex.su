stm32l4xx_ll_adc.h:2507:22:LL_<PERSON><PERSON>_SetCommonPathInternalCh	16	static
stm32l4xx_ll_adc.h:2588:26:LL_ADC_GetCommonPathInternalCh	16	static
stm32l4xx_ll_adc.h:2632:22:LL_<PERSON><PERSON>_SetCalibrationFactor	24	static
stm32l4xx_ll_adc.h:2655:26:LL_ADC_GetCalibrationFactor	16	static
stm32l4xx_ll_adc.h:2927:22:LL_ADC_SetOffset	32	static
stm32l4xx_ll_adc.h:3006:26:LL_ADC_GetOffsetChannel	24	static
stm32l4xx_ll_adc.h:3065:22:LL_ADC_SetOffsetState	32	static
stm32l4xx_ll_adc.h:3113:22:LL_ADC_SetSamplingTimeCommonConfig	16	static
stm32l4xx_ll_adc.h:3246:26:LL_<PERSON><PERSON>_REG_IsTriggerSourceSWStart	16	static
stm32l4xx_ll_adc.h:3972:26:LL_ADC_INJ_IsTriggerSourceSWStart	16	static
stm32l4xx_ll_adc.h:4288:26:LL_ADC_INJ_GetTrigAuto	16	static
stm32l4xx_ll_adc.h:4334:22:LL_ADC_INJ_SetQueueMode	16	static
stm32l4xx_ll_adc.h:4714:22:LL_ADC_SetChannelSamplingTime	32	static
stm32l4xx_ll_adc.h:4868:22:LL_ADC_SetChannelSingleDiff	24	static
stm32l4xx_ll_adc.h:5900:22:LL_ADC_EnableDeepPowerDown	16	static
stm32l4xx_ll_adc.h:5975:22:LL_ADC_DisableInternalRegulator	16	static
stm32l4xx_ll_adc.h:6046:26:LL_ADC_IsEnabled	16	static
stm32l4xx_ll_adc.h:6085:22:LL_ADC_StartCalibration	16	static
stm32l4xx_ll_adc.h:6101:26:LL_ADC_IsCalibrationOnGoing	16	static
stm32l4xx_ll_adc.h:6168:26:LL_ADC_REG_IsConversionOngoing	16	static
stm32l4xx_ll_adc.h:6315:22:LL_ADC_INJ_StartConversion	16	static
stm32l4xx_ll_adc.h:6351:26:LL_ADC_INJ_IsConversionOngoing	16	static
stm32l4xx_hal_adc_ex.c:128:19:HAL_ADCEx_Calibration_Start	24	static
stm32l4xx_hal_adc_ex.c:202:10:HAL_ADCEx_Calibration_GetValue	16	static
stm32l4xx_hal_adc_ex.c:222:19:HAL_ADCEx_Calibration_SetValue	40	static
stm32l4xx_hal_adc_ex.c:279:19:HAL_ADCEx_InjectedStart	24	static
stm32l4xx_hal_adc_ex.c:428:19:HAL_ADCEx_InjectedStop	24	static
stm32l4xx_hal_adc_ex.c:483:19:HAL_ADCEx_InjectedPollForConversion	40	static
stm32l4xx_hal_adc_ex.c:620:19:HAL_ADCEx_InjectedStart_IT	24	static
stm32l4xx_hal_adc_ex.c:793:19:HAL_ADCEx_InjectedStop_IT	24	static
stm32l4xx_hal_adc_ex.c:1135:10:HAL_ADCEx_InjectedGetValue	24	static
stm32l4xx_hal_adc_ex.c:1170:13:HAL_ADCEx_InjectedConvCpltCallback	16	static
stm32l4xx_hal_adc_ex.c:1189:13:HAL_ADCEx_InjectedQueueOverflowCallback	16	static
stm32l4xx_hal_adc_ex.c:1204:13:HAL_ADCEx_LevelOutOfWindow2Callback	16	static
stm32l4xx_hal_adc_ex.c:1219:13:HAL_ADCEx_LevelOutOfWindow3Callback	16	static
stm32l4xx_hal_adc_ex.c:1235:13:HAL_ADCEx_EndOfSamplingCallback	16	static
stm32l4xx_hal_adc_ex.c:1252:19:HAL_ADCEx_RegularStop	24	static
stm32l4xx_hal_adc_ex.c:1310:19:HAL_ADCEx_RegularStop_IT	24	static
stm32l4xx_hal_adc_ex.c:1370:19:HAL_ADCEx_RegularStop_DMA	24	static
stm32l4xx_hal_adc_ex.c:1631:19:HAL_ADCEx_InjectedConfigChannel	224	static,ignoring_inline_asm
stm32l4xx_hal_adc_ex.c:2189:19:HAL_ADCEx_EnableInjectedQueue	32	static
stm32l4xx_hal_adc_ex.c:2230:19:HAL_ADCEx_DisableInjectedQueue	32	static
stm32l4xx_hal_adc_ex.c:2267:19:HAL_ADCEx_DisableVoltageRegulator	24	static
stm32l4xx_hal_adc_ex.c:2304:19:HAL_ADCEx_EnterADCDeepPowerDownMode	24	static
