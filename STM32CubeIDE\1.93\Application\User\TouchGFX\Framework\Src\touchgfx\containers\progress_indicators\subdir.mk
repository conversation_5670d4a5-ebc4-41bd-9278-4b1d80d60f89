################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (9-2020-q2-update)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
CPP_SRCS += \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/AbstractDirectionProgress.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/AbstractProgressIndicator.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/BoxProgress.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/CircleProgress.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/ImageProgress.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/LineProgress.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/TextProgress.cpp 

OBJS += \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/AbstractDirectionProgress.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/AbstractProgressIndicator.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/BoxProgress.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/CircleProgress.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/ImageProgress.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/LineProgress.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/TextProgress.o 

CPP_DEPS += \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/AbstractDirectionProgress.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/AbstractProgressIndicator.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/BoxProgress.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/CircleProgress.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/ImageProgress.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/LineProgress.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/TextProgress.d 


# Each subdirectory must supply rules for building sources it contributes
Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/AbstractDirectionProgress.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/AbstractDirectionProgress.cpp Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/AbstractProgressIndicator.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/AbstractProgressIndicator.cpp Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/BoxProgress.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/BoxProgress.cpp Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/CircleProgress.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/CircleProgress.cpp Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/ImageProgress.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/ImageProgress.cpp Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/LineProgress.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/LineProgress.cpp Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/TextProgress.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/containers/progress_indicators/TextProgress.cpp Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

