Application/User/TouchGFX/target/generated/TouchGFXGeneratedHAL.o: \
 D:/spo2_zcross/max_zcrossing/TouchGFX/target/generated/TouchGFXGeneratedHAL.cpp \
 ../../TouchGFX/target/generated/TouchGFXGeneratedHAL.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/HAL.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Types.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Config.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Bitmap.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Drawable.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/ClickEvent.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Event.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/DragEvent.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/GestureEvent.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/BlitOp.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/DMA.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Atomic.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/FrameBufferAllocator.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Gestures.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/UIEventListener.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/lcd/LCD.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Font.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Unicode.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/TextProvider.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/TextureMapTypes.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/lcd/DebugPrinter.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/platform/core/MCUInstrumentation.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/platform/driver/button/ButtonController.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/platform/driver/touch/TouchController.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/OSWrappers.hpp \
 ../../TouchGFX/gui/include/gui/common/FrontendHeap.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/common/FrontendHeapBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/common/Meta.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/common/Partition.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/common/AbstractPartition.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/mvp/MVPHeap.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/NoTransition.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/Transition.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Application.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/Container.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Callback.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/CoverTransition.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/EasingEquations.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Widget.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/SlideTransition.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/SnapshotWidget.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/WipeTransition.hpp \
 ../../TouchGFX/gui/include/gui/common/FrontendApplication.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/common/FrontendApplicationBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/mvp/MVPApplication.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Screen.hpp \
 ../../TouchGFX/store/WidgetStore.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/mvp/Presenter.hpp \
 ../../TouchGFX/gui/include/gui/model/Model.hpp ../../Core/Inc/database.h \
 ../../Core/Inc/Awt_types.h ../../Core/Inc/watch_state.h \
 ../../Core/Inc/AWT_UI_Wireless.h \
 D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc/algohub_api.h \
 ../../Core/Boards/Inc/1.93/main.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h \
 ../../Core/Inc/stm32l4xx_hal_conf.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h \
 ../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h \
 ../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4r9xx.h \
 ../../Drivers/CMSIS/Include/core_cm4.h \
 ../../Drivers/CMSIS/Include/cmsis_version.h \
 ../../Drivers/CMSIS/Include/cmsis_compiler.h \
 ../../Drivers/CMSIS/Include/cmsis_gcc.h \
 ../../Drivers/CMSIS/Include/mpu_armv7.h \
 ../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_adc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_ll_adc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_adc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_crc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_crc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma2d.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dsi.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gfxmmu.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_lptim.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ltdc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ltdc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dsi.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ospi.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rtc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rtc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_sram.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_ll_fmc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h \
 ../../Core/Inc/ring_buf.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h \
 ../../Core/Inc/FreeRTOSConfig.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/task.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/list.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h \
 ../../Core/Inc/Awt_types.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/task.h \
 ../../TouchGFX/gui/include/gui/home_screen_screen/HOME_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/home_screen_screen/HOME_SCREENViewBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/mvp/View.hpp \
 ../../TouchGFX/gui/include/gui/home_screen_screen/HOME_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/model/ModelListener.hpp \
 ../../TouchGFX/gui/include/gui/PopupBase.h \
 ../../TouchGFX/gui/include/gui/containers/DefaultPopupContainer.hpp \
 ../../TouchGFX/gui/include/gui/containers/PopupContainer.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/PopupContainerBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/ModalWindow.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Box.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Image.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Color.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Button.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/AbstractButton.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/TextAreaWithWildcard.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/TextArea.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/TypedText.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Texts.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/Slider.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/AnimatedImage.hpp \
 ../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF/Awt_Vibrator.h \
 ../../Core/Inc/Flash_EEPROM.h ../../Core/Inc/database.h \
 ../../Core/Inc/AWT_UI_Wireless.h ../../Core/Inc/event_handler.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/semphr.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h \
 ../../Core/Inc/Flash_EEPROM.h ../../Core/Inc/Wireless.h \
 ../../Core/Inc/AWT_Boot_Option.h \
 ../../TouchGFX/gui/include/gui/StatusBarBase.h \
 ../../TouchGFX/gui/include/gui/containers/Battery_BT_container.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Battery_BT_containerBase.hpp \
 ../../TouchGFX/gui/include/gui/QuickMenuBase.h \
 ../../TouchGFX/gui/include/gui/containers/Quick_accessContainer.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Quick_accessContainerBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/SlideMenu.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/mixins/MoveAnimator.hpp \
 ../../TouchGFX/gui/include/gui/containers/SETTINGS_CHECKING_CONNECTIVITY.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/SETTINGS_CHECKING_CONNECTIVITYBase.hpp \
 ../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_TIME.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_TIMEBase.hpp \
 ../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_VITAL_SPO2.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_VITAL_SPO2Base.hpp \
 ../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_VITAL_PEDOMETER.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_VITAL_PEDOMETERBase.hpp \
 ../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_VITAL_HR.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_VITAL_HRBase.hpp \
 ../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_VITAL_TEMP.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_VITAL_TEMPBase.hpp \
 ../../TouchGFX/gui/include/gui/AppDrawerBase.h \
 ../../TouchGFX/gui/include/gui/containers/AppDrawerContainer.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/AppDrawerContainerBase.hpp \
 ../../TouchGFX/gui/include/gui/boot_screen_with_logo_screen/BOOT_SCREEN_WITH_LOGOView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/boot_screen_with_logo_screen/BOOT_SCREEN_WITH_LOGOViewBase.hpp \
 ../../TouchGFX/gui/include/gui/boot_screen_with_logo_screen/BOOT_SCREEN_WITH_LOGOPresenter.hpp \
 ../../Core/Inc/AWT_Boot_Option.h \
 ../../TouchGFX/gui/include/gui/one_time_charge_prompt_screen/ONE_TIME_CHARGE_PROMPTView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/one_time_charge_prompt_screen/ONE_TIME_CHARGE_PROMPTViewBase.hpp \
 ../../TouchGFX/gui/include/gui/one_time_charge_prompt_screen/ONE_TIME_CHARGE_PROMPTPresenter.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/mixins/FadeAnimator.hpp \
 ../../TouchGFX/gui/include/gui/setup_screen_1_screen/SETUP_SCREEN_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setup_screen_1_screen/SETUP_SCREEN_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setup_screen_1_screen/SETUP_SCREEN_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/setup_waiting_to_pair_screen_screen/SETUP_WAITING_TO_PAIR_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setup_waiting_to_pair_screen_screen/SETUP_WAITING_TO_PAIR_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setup_waiting_to_pair_screen_screen/SETUP_WAITING_TO_PAIR_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/bluetooth_passkey_screen_screen/BLUETOOTH_PASSKEY_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/bluetooth_passkey_screen_screen/BLUETOOTH_PASSKEY_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/bluetooth_passkey_screen_screen/BLUETOOTH_PASSKEY_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/setup_pairing_successful_screen_screen/SETUP_PAIRING_SUCCESSFUL_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setup_pairing_successful_screen_screen/SETUP_PAIRING_SUCCESSFUL_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setup_pairing_successful_screen_screen/SETUP_PAIRING_SUCCESSFUL_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/downloading_configurations_screen/DOWNLOADING_CONFIGURATIONSView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/downloading_configurations_screen/DOWNLOADING_CONFIGURATIONSViewBase.hpp \
 ../../TouchGFX/gui/include/gui/downloading_configurations_screen/DOWNLOADING_CONFIGURATIONSPresenter.hpp \
 ../../TouchGFX/gui/include/gui/configurations_downloaded_successful_screen_screen/CONFIGURATIONS_DOWNLOADED_SUCCESSFUL_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/configurations_downloaded_successful_screen_screen/CONFIGURATIONS_DOWNLOADED_SUCCESSFUL_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/configurations_downloaded_successful_screen_screen/CONFIGURATIONS_DOWNLOADED_SUCCESSFUL_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/setup_complete_screen/setup_completeView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setup_complete_screen/setup_completeViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setup_complete_screen/setup_completePresenter.hpp \
 ../../TouchGFX/gui/include/gui/power_off_conform_screen/POWER_OFF_CONFORMView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/power_off_conform_screen/POWER_OFF_CONFORMViewBase.hpp \
 ../../TouchGFX/gui/include/gui/power_off_conform_screen/POWER_OFF_CONFORMPresenter.hpp \
 ../../TouchGFX/gui/include/gui/restart_conform_screen/RESTART_CONFORMView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/restart_conform_screen/RESTART_CONFORMViewBase.hpp \
 ../../TouchGFX/gui/include/gui/restart_conform_screen/RESTART_CONFORMPresenter.hpp \
 ../../TouchGFX/gui/include/gui/settings_menu_screen/Settings_MenuView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/settings_menu_screen/Settings_MenuViewBase.hpp \
 ../../TouchGFX/gui/include/gui/settings_menu_screen/Settings_MenuPresenter.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/ScrollableContainer.hpp \
 ../../TouchGFX/gui/include/gui/containers/Settings_Menu_Container.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Settings_Menu_ContainerBase.hpp \
 ../../TouchGFX/gui/include/gui/blood_pressure_finger_prompt_pg6_screen/Blood_Pressure_Finger_prompt_pg6View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/blood_pressure_finger_prompt_pg6_screen/Blood_Pressure_Finger_prompt_pg6ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/blood_pressure_finger_prompt_pg6_screen/Blood_Pressure_Finger_prompt_pg6Presenter.hpp \
 ../../TouchGFX/gui/include/gui/popup_battery_charging_screen1_screen/Popup_Battery_Charging_Screen1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/popup_battery_charging_screen1_screen/Popup_Battery_Charging_Screen1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/popup_battery_charging_screen1_screen/Popup_Battery_Charging_Screen1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/time_container_bt_popup.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/time_container_bt_popupBase.hpp \
 ../../Core/Inc/watch_state.h \
 ../../TouchGFX/gui/include/gui/setting_reset_confirm_screen1_screen/Setting_Reset_Confirm_Screen1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setting_reset_confirm_screen1_screen/Setting_Reset_Confirm_Screen1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_reset_confirm_screen1_screen/Setting_Reset_Confirm_Screen1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/setting_reset_device_screen2_screen/Setting_Reset_Device_Screen2View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setting_reset_device_screen2_screen/Setting_Reset_Device_Screen2ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_reset_device_screen2_screen/Setting_Reset_Device_Screen2Presenter.hpp \
 ../../TouchGFX/gui/include/gui/setting_reset_device_screen2_1_screen/Setting_Reset_Device_Screen2_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setting_reset_device_screen2_1_screen/Setting_Reset_Device_Screen2_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_reset_device_screen2_1_screen/Setting_Reset_Device_Screen2_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/setting_temperature_screen_screen/Setting_Temperature_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setting_temperature_screen_screen/Setting_Temperature_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_temperature_screen_screen/Setting_Temperature_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/TEMPERATURE_FORMAT.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/TEMPERATURE_FORMATBase.hpp \
 ../../TouchGFX/gui/include/gui/settings_temperature_unit_screen/SETTINGS_TEMPERATURE_UNITView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/settings_temperature_unit_screen/SETTINGS_TEMPERATURE_UNITViewBase.hpp \
 ../../TouchGFX/gui/include/gui/settings_temperature_unit_screen/SETTINGS_TEMPERATURE_UNITPresenter.hpp \
 ../../TouchGFX/gui/include/gui/settings_time_format_screen/SETTINGS_TIME_FORMATView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/settings_time_format_screen/SETTINGS_TIME_FORMATViewBase.hpp \
 ../../TouchGFX/gui/include/gui/settings_time_format_screen/SETTINGS_TIME_FORMATPresenter.hpp \
 ../../TouchGFX/gui/include/gui/setting_time_format_screen_screen/Setting_Time_Format_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setting_time_format_screen_screen/Setting_Time_Format_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_time_format_screen_screen/Setting_Time_Format_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/TIME_FORMAT.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/TIME_FORMATBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_display_brightness_screen_screen/Setting_Display_Brightness_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setting_display_brightness_screen_screen/Setting_Display_Brightness_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_display_brightness_screen_screen/Setting_Display_Brightness_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/setting_about_device_screen_screen/Setting_About_Device_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setting_about_device_screen_screen/Setting_About_Device_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_about_device_screen_screen/Setting_About_Device_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/setting_bluetooth_screen1_screen/Setting_Bluetooth_Screen1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setting_bluetooth_screen1_screen/Setting_Bluetooth_Screen1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_bluetooth_screen1_screen/Setting_Bluetooth_Screen1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/user_details_screen_screen/User_Details_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/user_details_screen_screen/User_Details_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/user_details_screen_screen/User_Details_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/CustomContainer1.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/CustomContainer1Base.hpp \
 ../../TouchGFX/gui/include/gui/blood_pressure_screen/Blood_PressureView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/blood_pressure_screen/Blood_PressureViewBase.hpp \
 ../../TouchGFX/gui/include/gui/blood_pressure_screen/Blood_PressurePresenter.hpp \
 ../../TouchGFX/gui/include/gui/bp_progress_screen_screen/BP_Progress_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/bp_progress_screen_screen/BP_Progress_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/bp_progress_screen_screen/BP_Progress_ScreenPresenter.hpp \
 ../../Core/Inc/abortState.h \
 ../../TouchGFX/gui/include/gui/bp_history_screen_screen/BP_HISTORY_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/bp_history_screen_screen/BP_HISTORY_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/bp_history_screen_screen/BP_HISTORY_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/BP_History.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/BP_HistoryBase.hpp \
 ../../TouchGFX/gui/include/gui/blood_pressure_see_history_pg2_screen/Blood_Pressure_See_History_pg2View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/blood_pressure_see_history_pg2_screen/Blood_Pressure_See_History_pg2ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/blood_pressure_see_history_pg2_screen/Blood_Pressure_See_History_pg2Presenter.hpp \
 ../../TouchGFX/gui/include/gui/bp_error_1_screen/BP_ERROR_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/bp_error_1_screen/BP_ERROR_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/bp_error_1_screen/BP_ERROR_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/ecg_main_screen_screen/ECG_Main_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_main_screen_screen/ECG_Main_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/ecg_main_screen_screen/ECG_Main_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/ecg_progress_screen/ECG_PROGRESSView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_progress_screen/ECG_PROGRESSViewBase.hpp \
 ../../TouchGFX/gui/include/gui/ecg_progress_screen/ECG_PROGRESSPresenter.hpp \
 ../../TouchGFX/gui/include/gui/ecg_graph_display_screen_screen/ECG_Graph_Display_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_graph_display_screen_screen/ECG_Graph_Display_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/ecg_graph_display_screen_screen/ECG_Graph_Display_ScreenPresenter.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/ScalableImage.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/graph/GraphWrapAndOverwrite.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/graph/AbstractDataGraph.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/CWRUtil.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Utils.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Rasterizer.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Outline.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Cell.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Renderer.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/RenderingBuffer.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Scanline.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/CanvasWidgetRenderer.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/AbstractPainter.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/graph/GraphElements.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/Canvas.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/CanvasWidget.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/PainterRGB565.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/AbstractPainterRGB565.hpp \
 ../../TouchGFX/gui/include/gui/ecg_display_screen_screen/ECG_Display_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_display_screen_screen/ECG_Display_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/ecg_display_screen_screen/ECG_Display_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/ecg_inference_screen_screen/ECG_INFERENCE_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_inference_screen_screen/ECG_INFERENCE_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/ecg_inference_screen_screen/ECG_INFERENCE_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/ECG_INFERENCE_CONTAINER.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/ECG_INFERENCE_CONTAINERBase.hpp \
 ../../TouchGFX/gui/include/gui/ecg_error_1_screen/ECG_ERROR_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_error_1_screen/ECG_ERROR_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/ecg_error_1_screen/ECG_ERROR_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/ecg_finger_prompt_screen/ECG_FINGER_PROMPTView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_finger_prompt_screen/ECG_FINGER_PROMPTViewBase.hpp \
 ../../TouchGFX/gui/include/gui/ecg_finger_prompt_screen/ECG_FINGER_PROMPTPresenter.hpp \
 ../../TouchGFX/gui/include/gui/blood_oxygen_main_screen_screen/Blood_Oxygen_Main_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/blood_oxygen_main_screen_screen/Blood_Oxygen_Main_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/blood_oxygen_main_screen_screen/Blood_Oxygen_Main_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/blood_oxygen_history_screen_screen/BLOOD_OXYGEN_HISTORY_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/blood_oxygen_history_screen_screen/BLOOD_OXYGEN_HISTORY_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/blood_oxygen_history_screen_screen/BLOOD_OXYGEN_HISTORY_SCREENPresenter.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/graph/GraphScroll.hpp \
 ../../TouchGFX/gui/include/gui/spo2_progress_screen_screen/Spo2_Progress_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/spo2_progress_screen_screen/Spo2_Progress_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/spo2_progress_screen_screen/Spo2_Progress_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/spo2_error_1_screen/SPO2_ERROR_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/spo2_error_1_screen/SPO2_ERROR_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/spo2_error_1_screen/SPO2_ERROR_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/blood_oxygen_screen/Blood_OxygenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/blood_oxygen_screen/Blood_OxygenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/blood_oxygen_screen/Blood_OxygenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/hr_main_screen_screen/HR_Main_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/hr_main_screen_screen/HR_Main_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/hr_main_screen_screen/HR_Main_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/hr_progress_screen_screen/HR_Progress_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/hr_progress_screen_screen/HR_Progress_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/hr_progress_screen_screen/HR_Progress_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/hr_history_screen_screen/HR_HISTORY_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/hr_history_screen_screen/HR_HISTORY_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/hr_history_screen_screen/HR_HISTORY_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/no_history_screen_screen/NO_HISTORY_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/no_history_screen_screen/NO_HISTORY_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/no_history_screen_screen/NO_HISTORY_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/hr_error_1_screen/HR_ERROR_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/hr_error_1_screen/HR_ERROR_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/hr_error_1_screen/HR_ERROR_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/heart_rate_screen/Heart_rateView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/heart_rate_screen/Heart_rateViewBase.hpp \
 ../../TouchGFX/gui/include/gui/heart_rate_screen/Heart_ratePresenter.hpp \
 ../../TouchGFX/gui/include/gui/setting_wifi_screen_screen/SETTING_WIFI_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/setting_wifi_screen_screen/SETTING_WIFI_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/setting_wifi_screen_screen/SETTING_WIFI_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/checking_buffer_screen_screen/CHECKING_BUFFER_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/checking_buffer_screen_screen/CHECKING_BUFFER_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/checking_buffer_screen_screen/CHECKING_BUFFER_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/updating_buffer_screen_screen/UPDATING_BUFFER_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/updating_buffer_screen_screen/UPDATING_BUFFER_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/updating_buffer_screen_screen/UPDATING_BUFFER_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/no_updates_available_screen_screen/NO_UPDATES_AVAILABLE_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/no_updates_available_screen_screen/NO_UPDATES_AVAILABLE_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/no_updates_available_screen_screen/NO_UPDATES_AVAILABLE_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/initate_software_update_screen_1_screen/INITATE_SOFTWARE_UPDATE_SCREEN_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/initate_software_update_screen_1_screen/INITATE_SOFTWARE_UPDATE_SCREEN_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/initate_software_update_screen_1_screen/INITATE_SOFTWARE_UPDATE_SCREEN_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/software_update_main_screen_screen/SOFTWARE_UPDATE_MAIN_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/software_update_main_screen_screen/SOFTWARE_UPDATE_MAIN_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/software_update_main_screen_screen/SOFTWARE_UPDATE_MAIN_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/unable_to_check_for_updates_screen_screen/UNABLE_TO_CHECK_FOR_UPDATES_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/unable_to_check_for_updates_screen_screen/UNABLE_TO_CHECK_FOR_UPDATES_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/unable_to_check_for_updates_screen_screen/UNABLE_TO_CHECK_FOR_UPDATES_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/update_available_screen_screen/UPDATE_AVAILABLE_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/update_available_screen_screen/UPDATE_AVAILABLE_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/update_available_screen_screen/UPDATE_AVAILABLE_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/watch_is_updating_screen_screen/WATCH_IS_UPDATING_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/watch_is_updating_screen_screen/WATCH_IS_UPDATING_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/watch_is_updating_screen_screen/WATCH_IS_UPDATING_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/vibration_main_screen_screen/VIBRATION_MAIN_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/vibration_main_screen_screen/VIBRATION_MAIN_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/vibration_main_screen_screen/VIBRATION_MAIN_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/VIBRATION.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/VIBRATIONBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/ToggleButton.hpp \
 ../../TouchGFX/gui/include/gui/pedometer_main_screen_screen/PEDOMETER_MAIN_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/pedometer_main_screen_screen/PEDOMETER_MAIN_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/pedometer_main_screen_screen/PEDOMETER_MAIN_SCREENPresenter.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/progress_indicators/LineProgress.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/progress_indicators/AbstractProgressIndicator.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/Line.hpp \
 ../../TouchGFX/gui/include/gui/reference_screen/ReferenceView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/reference_screen/ReferenceViewBase.hpp \
 ../../TouchGFX/gui/include/gui/reference_screen/ReferencePresenter.hpp \
 ../../TouchGFX/gui/include/gui/pedometer_reset_goal_screen_screen/PEDOMETER_RESET_GOAL_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/pedometer_reset_goal_screen_screen/PEDOMETER_RESET_GOAL_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/pedometer_reset_goal_screen_screen/PEDOMETER_RESET_GOAL_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/pedometer_settings_screen_screen/PEDOMETER_SETTINGS_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/pedometer_settings_screen_screen/PEDOMETER_SETTINGS_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/pedometer_settings_screen_screen/PEDOMETER_SETTINGS_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/pedometer_set_goal_screen_screen/PEDOMETER_SET_GOAL_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/pedometer_set_goal_screen_screen/PEDOMETER_SET_GOAL_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/pedometer_set_goal_screen_screen/PEDOMETER_SET_GOAL_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/cbt_progress_screen_screen/CBT_Progress_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/cbt_progress_screen_screen/CBT_Progress_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/cbt_progress_screen_screen/CBT_Progress_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/temperature_normal_screen/Temperature_NormalView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/temperature_normal_screen/Temperature_NormalViewBase.hpp \
 ../../TouchGFX/gui/include/gui/temperature_normal_screen/Temperature_NormalPresenter.hpp \
 ../../TouchGFX/gui/include/gui/cbt_error_1_screen/CBT_ERROR_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/cbt_error_1_screen/CBT_ERROR_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/cbt_error_1_screen/CBT_ERROR_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/settins_screen_timeout_1_screen/SETTINS_SCREEN_TIMEOUT_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/settins_screen_timeout_1_screen/SETTINS_SCREEN_TIMEOUT_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/settins_screen_timeout_1_screen/SETTINS_SCREEN_TIMEOUT_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/settins_screen_timeout_2_screen/SETTINS_SCREEN_TIMEOUT_2View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/settins_screen_timeout_2_screen/SETTINS_SCREEN_TIMEOUT_2ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/settins_screen_timeout_2_screen/SETTINS_SCREEN_TIMEOUT_2Presenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/SCREEN_TIMEOUT.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/SCREEN_TIMEOUTBase.hpp \
 ../../TouchGFX/gui/include/gui/watch_face_1_screen/WATCH_FACE_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/watch_face_1_screen/WATCH_FACE_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/watch_face_1_screen/WATCH_FACE_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/WatchFacePopupBase.h \
 ../../TouchGFX/gui/include/gui/containers/WatchFacePopupContainer.hpp \
 ../../TouchGFX/gui/include/gui/settings_watch_face1_screen/SETTINGS_WATCH_FACE1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/settings_watch_face1_screen/SETTINGS_WATCH_FACE1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/settings_watch_face1_screen/SETTINGS_WATCH_FACE1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/watch_face_done_screen/WATCH_FACE_DONEView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/watch_face_done_screen/WATCH_FACE_DONEViewBase.hpp \
 ../../TouchGFX/gui/include/gui/watch_face_done_screen/WATCH_FACE_DONEPresenter.hpp \
 ../../TouchGFX/gui/include/gui/home_passkey_reset_screen/HOME_PASSKEY_RESETView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/home_passkey_reset_screen/HOME_PASSKEY_RESETViewBase.hpp \
 ../../TouchGFX/gui/include/gui/home_passkey_reset_screen/HOME_PASSKEY_RESETPresenter.hpp \
 ../../TouchGFX/gui/include/gui/settings_passcode_enable_screen/SETTINGS_PASSCODE_ENABLEView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/settings_passcode_enable_screen/SETTINGS_PASSCODE_ENABLEViewBase.hpp \
 ../../TouchGFX/gui/include/gui/settings_passcode_enable_screen/SETTINGS_PASSCODE_ENABLEPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/PasscodeContainer.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/PasscodeContainerBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/ButtonWithLabel.hpp \
 ../../TouchGFX/gui/include/gui/bluetooth_turned_off_screen_screen/BLUETOOTH_TURNED_OFF_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/bluetooth_turned_off_screen_screen/BLUETOOTH_TURNED_OFF_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/bluetooth_turned_off_screen_screen/BLUETOOTH_TURNED_OFF_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/developer_mode_screen/DEVELOPER_MODEView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/developer_mode_screen/DEVELOPER_MODEViewBase.hpp \
 ../../TouchGFX/gui/include/gui/developer_mode_screen/DEVELOPER_MODEPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/DeveloperMode.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/DeveloperModeBase.hpp \
 ../../TouchGFX/gui/include/gui/wifi_flash_screen/WIFI_FLASHView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/wifi_flash_screen/WIFI_FLASHViewBase.hpp \
 ../../TouchGFX/gui/include/gui/wifi_flash_screen/WIFI_FLASHPresenter.hpp \
 ../../TouchGFX/gui/include/gui/vital_sleep_screen/VITAL_SLEEPView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/vital_sleep_screen/VITAL_SLEEPViewBase.hpp \
 ../../TouchGFX/gui/include/gui/vital_sleep_screen/VITAL_SLEEPPresenter.hpp \
 ../../TouchGFX/gui/include/gui/coredump_screen_screen/coredump_ScreenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/coredump_screen_screen/coredump_ScreenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/coredump_screen_screen/coredump_ScreenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/cbt_main_screen_1_screen/CBT_Main_Screen_1View.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/cbt_main_screen_1_screen/CBT_Main_Screen_1ViewBase.hpp \
 ../../TouchGFX/gui/include/gui/cbt_main_screen_1_screen/CBT_Main_Screen_1Presenter.hpp \
 ../../TouchGFX/gui/include/gui/cbt_history_celsius_fahrenhit_history_screen/CBT_HISTORY_CELSIUS_FAHRENHIT_HISTORYView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/cbt_history_celsius_fahrenhit_history_screen/CBT_HISTORY_CELSIUS_FAHRENHIT_HISTORYViewBase.hpp \
 ../../TouchGFX/gui/include/gui/cbt_history_celsius_fahrenhit_history_screen/CBT_HISTORY_CELSIUS_FAHRENHIT_HISTORYPresenter.hpp \
 ../../TouchGFX/gui/include/gui/calibration_screen/CALIBRATIONView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/calibration_screen/CALIBRATIONViewBase.hpp \
 ../../TouchGFX/gui/include/gui/calibration_screen/CALIBRATIONPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/CALIBRATION.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/CALIBRATIONBase.hpp \
 ../../TouchGFX/gui/include/gui/sos_main_screen/SOS_MAINView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/sos_main_screen/SOS_MAINViewBase.hpp \
 ../../TouchGFX/gui/include/gui/sos_main_screen/SOS_MAINPresenter.hpp \
 ../../TouchGFX/gui/include/gui/gsr_screen/GSRView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/gsr_screen/GSRViewBase.hpp \
 ../../TouchGFX/gui/include/gui/gsr_screen/GSRPresenter.hpp \
 ../../TouchGFX/gui/include/gui/settings_wifi_scanning_screen/SETTINGS_WIFI_SCANNINGView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/settings_wifi_scanning_screen/SETTINGS_WIFI_SCANNINGViewBase.hpp \
 ../../TouchGFX/gui/include/gui/settings_wifi_scanning_screen/SETTINGS_WIFI_SCANNINGPresenter.hpp \
 ../../TouchGFX/gui/include/gui/watchface_passcode_screen/WATCHFACE_PASSCODEView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/watchface_passcode_screen/WATCHFACE_PASSCODEViewBase.hpp \
 ../../TouchGFX/gui/include/gui/watchface_passcode_screen/WATCHFACE_PASSCODEPresenter.hpp \
 ../../TouchGFX/gui/include/gui/comm_state_screen_screen/COMM_STATE_SCREENView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/comm_state_screen_screen/COMM_STATE_SCREENViewBase.hpp \
 ../../TouchGFX/gui/include/gui/comm_state_screen_screen/COMM_STATE_SCREENPresenter.hpp \
 ../../TouchGFX/gui/include/gui/containers/COMMStateContainer.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/COMMStateContainerBase.hpp \
 ../../TouchGFX/gui/include/gui/spo2_max_screen/Spo2_MAXView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/spo2_max_screen/Spo2_MAXViewBase.hpp \
 ../../TouchGFX/gui/include/gui/spo2_max_screen/Spo2_MAXPresenter.hpp

../../TouchGFX/target/generated/TouchGFXGeneratedHAL.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/HAL.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Types.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Config.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Bitmap.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Drawable.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/ClickEvent.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Event.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/DragEvent.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/GestureEvent.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/BlitOp.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/DMA.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Atomic.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/FrameBufferAllocator.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Gestures.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/UIEventListener.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/lcd/LCD.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Font.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Unicode.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/TextProvider.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/TextureMapTypes.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/lcd/DebugPrinter.hpp:

../../Middlewares/ST/touchgfx/framework/include/platform/core/MCUInstrumentation.hpp:

../../Middlewares/ST/touchgfx/framework/include/platform/driver/button/ButtonController.hpp:

../../Middlewares/ST/touchgfx/framework/include/platform/driver/touch/TouchController.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/OSWrappers.hpp:

../../TouchGFX/gui/include/gui/common/FrontendHeap.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/common/FrontendHeapBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/common/Meta.hpp:

../../Middlewares/ST/touchgfx/framework/include/common/Partition.hpp:

../../Middlewares/ST/touchgfx/framework/include/common/AbstractPartition.hpp:

../../Middlewares/ST/touchgfx/framework/include/mvp/MVPHeap.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/NoTransition.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/Transition.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Application.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/Container.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Callback.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/CoverTransition.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/EasingEquations.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Widget.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/SlideTransition.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/SnapshotWidget.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/WipeTransition.hpp:

../../TouchGFX/gui/include/gui/common/FrontendApplication.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/common/FrontendApplicationBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/mvp/MVPApplication.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Screen.hpp:

../../TouchGFX/store/WidgetStore.hpp:

../../Middlewares/ST/touchgfx/framework/include/mvp/Presenter.hpp:

../../TouchGFX/gui/include/gui/model/Model.hpp:

../../Core/Inc/database.h:

../../Core/Inc/Awt_types.h:

../../Core/Inc/watch_state.h:

../../Core/Inc/AWT_UI_Wireless.h:

D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc/algohub_api.h:

../../Core/Boards/Inc/1.93/main.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h:

../../Core/Inc/stm32l4xx_hal_conf.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h:

../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h:

../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4r9xx.h:

../../Drivers/CMSIS/Include/core_cm4.h:

../../Drivers/CMSIS/Include/cmsis_version.h:

../../Drivers/CMSIS/Include/cmsis_compiler.h:

../../Drivers/CMSIS/Include/cmsis_gcc.h:

../../Drivers/CMSIS/Include/mpu_armv7.h:

../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_adc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_ll_adc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_adc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_crc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_crc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma2d.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dsi.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gfxmmu.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_lptim.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ltdc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ltdc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dsi.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ospi.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rtc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rtc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_sram.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_ll_fmc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h:

../../Core/Inc/ring_buf.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h:

../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h:

../../Core/Inc/FreeRTOSConfig.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h:

../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/task.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/list.h:

../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h:

../../Core/Inc/Awt_types.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/task.h:

../../TouchGFX/gui/include/gui/home_screen_screen/HOME_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/home_screen_screen/HOME_SCREENViewBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/mvp/View.hpp:

../../TouchGFX/gui/include/gui/home_screen_screen/HOME_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/model/ModelListener.hpp:

../../TouchGFX/gui/include/gui/PopupBase.h:

../../TouchGFX/gui/include/gui/containers/DefaultPopupContainer.hpp:

../../TouchGFX/gui/include/gui/containers/PopupContainer.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/PopupContainerBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/ModalWindow.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Box.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Image.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Color.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Button.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/AbstractButton.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/TextAreaWithWildcard.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/TextArea.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/TypedText.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Texts.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/Slider.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/AnimatedImage.hpp:

../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF/Awt_Vibrator.h:

../../Core/Inc/Flash_EEPROM.h:

../../Core/Inc/database.h:

../../Core/Inc/AWT_UI_Wireless.h:

../../Core/Inc/event_handler.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/semphr.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h:

../../Core/Inc/Flash_EEPROM.h:

../../Core/Inc/Wireless.h:

../../Core/Inc/AWT_Boot_Option.h:

../../TouchGFX/gui/include/gui/StatusBarBase.h:

../../TouchGFX/gui/include/gui/containers/Battery_BT_container.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Battery_BT_containerBase.hpp:

../../TouchGFX/gui/include/gui/QuickMenuBase.h:

../../TouchGFX/gui/include/gui/containers/Quick_accessContainer.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Quick_accessContainerBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/SlideMenu.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/mixins/MoveAnimator.hpp:

../../TouchGFX/gui/include/gui/containers/SETTINGS_CHECKING_CONNECTIVITY.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/SETTINGS_CHECKING_CONNECTIVITYBase.hpp:

../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_TIME.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_TIMEBase.hpp:

../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_VITAL_SPO2.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_VITAL_SPO2Base.hpp:

../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_VITAL_PEDOMETER.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_VITAL_PEDOMETERBase.hpp:

../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_VITAL_HR.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_VITAL_HRBase.hpp:

../../TouchGFX/gui/include/gui/containers/HOME_SCREEN_VITAL_TEMP.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/HOME_SCREEN_VITAL_TEMPBase.hpp:

../../TouchGFX/gui/include/gui/AppDrawerBase.h:

../../TouchGFX/gui/include/gui/containers/AppDrawerContainer.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/AppDrawerContainerBase.hpp:

../../TouchGFX/gui/include/gui/boot_screen_with_logo_screen/BOOT_SCREEN_WITH_LOGOView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/boot_screen_with_logo_screen/BOOT_SCREEN_WITH_LOGOViewBase.hpp:

../../TouchGFX/gui/include/gui/boot_screen_with_logo_screen/BOOT_SCREEN_WITH_LOGOPresenter.hpp:

../../Core/Inc/AWT_Boot_Option.h:

../../TouchGFX/gui/include/gui/one_time_charge_prompt_screen/ONE_TIME_CHARGE_PROMPTView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/one_time_charge_prompt_screen/ONE_TIME_CHARGE_PROMPTViewBase.hpp:

../../TouchGFX/gui/include/gui/one_time_charge_prompt_screen/ONE_TIME_CHARGE_PROMPTPresenter.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/mixins/FadeAnimator.hpp:

../../TouchGFX/gui/include/gui/setup_screen_1_screen/SETUP_SCREEN_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setup_screen_1_screen/SETUP_SCREEN_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/setup_screen_1_screen/SETUP_SCREEN_1Presenter.hpp:

../../TouchGFX/gui/include/gui/setup_waiting_to_pair_screen_screen/SETUP_WAITING_TO_PAIR_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setup_waiting_to_pair_screen_screen/SETUP_WAITING_TO_PAIR_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/setup_waiting_to_pair_screen_screen/SETUP_WAITING_TO_PAIR_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/bluetooth_passkey_screen_screen/BLUETOOTH_PASSKEY_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/bluetooth_passkey_screen_screen/BLUETOOTH_PASSKEY_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/bluetooth_passkey_screen_screen/BLUETOOTH_PASSKEY_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/setup_pairing_successful_screen_screen/SETUP_PAIRING_SUCCESSFUL_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setup_pairing_successful_screen_screen/SETUP_PAIRING_SUCCESSFUL_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/setup_pairing_successful_screen_screen/SETUP_PAIRING_SUCCESSFUL_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/downloading_configurations_screen/DOWNLOADING_CONFIGURATIONSView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/downloading_configurations_screen/DOWNLOADING_CONFIGURATIONSViewBase.hpp:

../../TouchGFX/gui/include/gui/downloading_configurations_screen/DOWNLOADING_CONFIGURATIONSPresenter.hpp:

../../TouchGFX/gui/include/gui/configurations_downloaded_successful_screen_screen/CONFIGURATIONS_DOWNLOADED_SUCCESSFUL_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/configurations_downloaded_successful_screen_screen/CONFIGURATIONS_DOWNLOADED_SUCCESSFUL_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/configurations_downloaded_successful_screen_screen/CONFIGURATIONS_DOWNLOADED_SUCCESSFUL_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/setup_complete_screen/setup_completeView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setup_complete_screen/setup_completeViewBase.hpp:

../../TouchGFX/gui/include/gui/setup_complete_screen/setup_completePresenter.hpp:

../../TouchGFX/gui/include/gui/power_off_conform_screen/POWER_OFF_CONFORMView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/power_off_conform_screen/POWER_OFF_CONFORMViewBase.hpp:

../../TouchGFX/gui/include/gui/power_off_conform_screen/POWER_OFF_CONFORMPresenter.hpp:

../../TouchGFX/gui/include/gui/restart_conform_screen/RESTART_CONFORMView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/restart_conform_screen/RESTART_CONFORMViewBase.hpp:

../../TouchGFX/gui/include/gui/restart_conform_screen/RESTART_CONFORMPresenter.hpp:

../../TouchGFX/gui/include/gui/settings_menu_screen/Settings_MenuView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/settings_menu_screen/Settings_MenuViewBase.hpp:

../../TouchGFX/gui/include/gui/settings_menu_screen/Settings_MenuPresenter.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/ScrollableContainer.hpp:

../../TouchGFX/gui/include/gui/containers/Settings_Menu_Container.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Settings_Menu_ContainerBase.hpp:

../../TouchGFX/gui/include/gui/blood_pressure_finger_prompt_pg6_screen/Blood_Pressure_Finger_prompt_pg6View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/blood_pressure_finger_prompt_pg6_screen/Blood_Pressure_Finger_prompt_pg6ViewBase.hpp:

../../TouchGFX/gui/include/gui/blood_pressure_finger_prompt_pg6_screen/Blood_Pressure_Finger_prompt_pg6Presenter.hpp:

../../TouchGFX/gui/include/gui/popup_battery_charging_screen1_screen/Popup_Battery_Charging_Screen1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/popup_battery_charging_screen1_screen/Popup_Battery_Charging_Screen1ViewBase.hpp:

../../TouchGFX/gui/include/gui/popup_battery_charging_screen1_screen/Popup_Battery_Charging_Screen1Presenter.hpp:

../../TouchGFX/gui/include/gui/containers/time_container_bt_popup.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/time_container_bt_popupBase.hpp:

../../Core/Inc/watch_state.h:

../../TouchGFX/gui/include/gui/setting_reset_confirm_screen1_screen/Setting_Reset_Confirm_Screen1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setting_reset_confirm_screen1_screen/Setting_Reset_Confirm_Screen1ViewBase.hpp:

../../TouchGFX/gui/include/gui/setting_reset_confirm_screen1_screen/Setting_Reset_Confirm_Screen1Presenter.hpp:

../../TouchGFX/gui/include/gui/setting_reset_device_screen2_screen/Setting_Reset_Device_Screen2View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setting_reset_device_screen2_screen/Setting_Reset_Device_Screen2ViewBase.hpp:

../../TouchGFX/gui/include/gui/setting_reset_device_screen2_screen/Setting_Reset_Device_Screen2Presenter.hpp:

../../TouchGFX/gui/include/gui/setting_reset_device_screen2_1_screen/Setting_Reset_Device_Screen2_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setting_reset_device_screen2_1_screen/Setting_Reset_Device_Screen2_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/setting_reset_device_screen2_1_screen/Setting_Reset_Device_Screen2_1Presenter.hpp:

../../TouchGFX/gui/include/gui/setting_temperature_screen_screen/Setting_Temperature_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setting_temperature_screen_screen/Setting_Temperature_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/setting_temperature_screen_screen/Setting_Temperature_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/TEMPERATURE_FORMAT.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/TEMPERATURE_FORMATBase.hpp:

../../TouchGFX/gui/include/gui/settings_temperature_unit_screen/SETTINGS_TEMPERATURE_UNITView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/settings_temperature_unit_screen/SETTINGS_TEMPERATURE_UNITViewBase.hpp:

../../TouchGFX/gui/include/gui/settings_temperature_unit_screen/SETTINGS_TEMPERATURE_UNITPresenter.hpp:

../../TouchGFX/gui/include/gui/settings_time_format_screen/SETTINGS_TIME_FORMATView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/settings_time_format_screen/SETTINGS_TIME_FORMATViewBase.hpp:

../../TouchGFX/gui/include/gui/settings_time_format_screen/SETTINGS_TIME_FORMATPresenter.hpp:

../../TouchGFX/gui/include/gui/setting_time_format_screen_screen/Setting_Time_Format_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setting_time_format_screen_screen/Setting_Time_Format_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/setting_time_format_screen_screen/Setting_Time_Format_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/TIME_FORMAT.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/TIME_FORMATBase.hpp:

../../TouchGFX/gui/include/gui/setting_display_brightness_screen_screen/Setting_Display_Brightness_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setting_display_brightness_screen_screen/Setting_Display_Brightness_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/setting_display_brightness_screen_screen/Setting_Display_Brightness_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/setting_about_device_screen_screen/Setting_About_Device_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setting_about_device_screen_screen/Setting_About_Device_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/setting_about_device_screen_screen/Setting_About_Device_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/setting_bluetooth_screen1_screen/Setting_Bluetooth_Screen1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setting_bluetooth_screen1_screen/Setting_Bluetooth_Screen1ViewBase.hpp:

../../TouchGFX/gui/include/gui/setting_bluetooth_screen1_screen/Setting_Bluetooth_Screen1Presenter.hpp:

../../TouchGFX/gui/include/gui/user_details_screen_screen/User_Details_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/user_details_screen_screen/User_Details_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/user_details_screen_screen/User_Details_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/CustomContainer1.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/CustomContainer1Base.hpp:

../../TouchGFX/gui/include/gui/blood_pressure_screen/Blood_PressureView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/blood_pressure_screen/Blood_PressureViewBase.hpp:

../../TouchGFX/gui/include/gui/blood_pressure_screen/Blood_PressurePresenter.hpp:

../../TouchGFX/gui/include/gui/bp_progress_screen_screen/BP_Progress_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/bp_progress_screen_screen/BP_Progress_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/bp_progress_screen_screen/BP_Progress_ScreenPresenter.hpp:

../../Core/Inc/abortState.h:

../../TouchGFX/gui/include/gui/bp_history_screen_screen/BP_HISTORY_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/bp_history_screen_screen/BP_HISTORY_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/bp_history_screen_screen/BP_HISTORY_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/BP_History.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/BP_HistoryBase.hpp:

../../TouchGFX/gui/include/gui/blood_pressure_see_history_pg2_screen/Blood_Pressure_See_History_pg2View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/blood_pressure_see_history_pg2_screen/Blood_Pressure_See_History_pg2ViewBase.hpp:

../../TouchGFX/gui/include/gui/blood_pressure_see_history_pg2_screen/Blood_Pressure_See_History_pg2Presenter.hpp:

../../TouchGFX/gui/include/gui/bp_error_1_screen/BP_ERROR_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/bp_error_1_screen/BP_ERROR_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/bp_error_1_screen/BP_ERROR_1Presenter.hpp:

../../TouchGFX/gui/include/gui/ecg_main_screen_screen/ECG_Main_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_main_screen_screen/ECG_Main_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/ecg_main_screen_screen/ECG_Main_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/ecg_progress_screen/ECG_PROGRESSView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_progress_screen/ECG_PROGRESSViewBase.hpp:

../../TouchGFX/gui/include/gui/ecg_progress_screen/ECG_PROGRESSPresenter.hpp:

../../TouchGFX/gui/include/gui/ecg_graph_display_screen_screen/ECG_Graph_Display_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_graph_display_screen_screen/ECG_Graph_Display_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/ecg_graph_display_screen_screen/ECG_Graph_Display_ScreenPresenter.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/ScalableImage.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/graph/GraphWrapAndOverwrite.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/graph/AbstractDataGraph.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/CWRUtil.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Utils.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Rasterizer.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Outline.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Cell.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Renderer.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/RenderingBuffer.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/Scanline.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/canvas_widget_renderer/CanvasWidgetRenderer.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/AbstractPainter.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/graph/GraphElements.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/Canvas.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/CanvasWidget.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/PainterRGB565.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/AbstractPainterRGB565.hpp:

../../TouchGFX/gui/include/gui/ecg_display_screen_screen/ECG_Display_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_display_screen_screen/ECG_Display_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/ecg_display_screen_screen/ECG_Display_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/ecg_inference_screen_screen/ECG_INFERENCE_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_inference_screen_screen/ECG_INFERENCE_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/ecg_inference_screen_screen/ECG_INFERENCE_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/ECG_INFERENCE_CONTAINER.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/ECG_INFERENCE_CONTAINERBase.hpp:

../../TouchGFX/gui/include/gui/ecg_error_1_screen/ECG_ERROR_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_error_1_screen/ECG_ERROR_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/ecg_error_1_screen/ECG_ERROR_1Presenter.hpp:

../../TouchGFX/gui/include/gui/ecg_finger_prompt_screen/ECG_FINGER_PROMPTView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/ecg_finger_prompt_screen/ECG_FINGER_PROMPTViewBase.hpp:

../../TouchGFX/gui/include/gui/ecg_finger_prompt_screen/ECG_FINGER_PROMPTPresenter.hpp:

../../TouchGFX/gui/include/gui/blood_oxygen_main_screen_screen/Blood_Oxygen_Main_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/blood_oxygen_main_screen_screen/Blood_Oxygen_Main_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/blood_oxygen_main_screen_screen/Blood_Oxygen_Main_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/blood_oxygen_history_screen_screen/BLOOD_OXYGEN_HISTORY_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/blood_oxygen_history_screen_screen/BLOOD_OXYGEN_HISTORY_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/blood_oxygen_history_screen_screen/BLOOD_OXYGEN_HISTORY_SCREENPresenter.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/graph/GraphScroll.hpp:

../../TouchGFX/gui/include/gui/spo2_progress_screen_screen/Spo2_Progress_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/spo2_progress_screen_screen/Spo2_Progress_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/spo2_progress_screen_screen/Spo2_Progress_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/spo2_error_1_screen/SPO2_ERROR_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/spo2_error_1_screen/SPO2_ERROR_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/spo2_error_1_screen/SPO2_ERROR_1Presenter.hpp:

../../TouchGFX/gui/include/gui/blood_oxygen_screen/Blood_OxygenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/blood_oxygen_screen/Blood_OxygenViewBase.hpp:

../../TouchGFX/gui/include/gui/blood_oxygen_screen/Blood_OxygenPresenter.hpp:

../../TouchGFX/gui/include/gui/hr_main_screen_screen/HR_Main_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/hr_main_screen_screen/HR_Main_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/hr_main_screen_screen/HR_Main_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/hr_progress_screen_screen/HR_Progress_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/hr_progress_screen_screen/HR_Progress_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/hr_progress_screen_screen/HR_Progress_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/hr_history_screen_screen/HR_HISTORY_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/hr_history_screen_screen/HR_HISTORY_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/hr_history_screen_screen/HR_HISTORY_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/no_history_screen_screen/NO_HISTORY_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/no_history_screen_screen/NO_HISTORY_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/no_history_screen_screen/NO_HISTORY_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/hr_error_1_screen/HR_ERROR_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/hr_error_1_screen/HR_ERROR_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/hr_error_1_screen/HR_ERROR_1Presenter.hpp:

../../TouchGFX/gui/include/gui/heart_rate_screen/Heart_rateView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/heart_rate_screen/Heart_rateViewBase.hpp:

../../TouchGFX/gui/include/gui/heart_rate_screen/Heart_ratePresenter.hpp:

../../TouchGFX/gui/include/gui/setting_wifi_screen_screen/SETTING_WIFI_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/setting_wifi_screen_screen/SETTING_WIFI_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/setting_wifi_screen_screen/SETTING_WIFI_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/checking_buffer_screen_screen/CHECKING_BUFFER_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/checking_buffer_screen_screen/CHECKING_BUFFER_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/checking_buffer_screen_screen/CHECKING_BUFFER_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/updating_buffer_screen_screen/UPDATING_BUFFER_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/updating_buffer_screen_screen/UPDATING_BUFFER_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/updating_buffer_screen_screen/UPDATING_BUFFER_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/no_updates_available_screen_screen/NO_UPDATES_AVAILABLE_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/no_updates_available_screen_screen/NO_UPDATES_AVAILABLE_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/no_updates_available_screen_screen/NO_UPDATES_AVAILABLE_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/initate_software_update_screen_1_screen/INITATE_SOFTWARE_UPDATE_SCREEN_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/initate_software_update_screen_1_screen/INITATE_SOFTWARE_UPDATE_SCREEN_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/initate_software_update_screen_1_screen/INITATE_SOFTWARE_UPDATE_SCREEN_1Presenter.hpp:

../../TouchGFX/gui/include/gui/software_update_main_screen_screen/SOFTWARE_UPDATE_MAIN_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/software_update_main_screen_screen/SOFTWARE_UPDATE_MAIN_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/software_update_main_screen_screen/SOFTWARE_UPDATE_MAIN_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/unable_to_check_for_updates_screen_screen/UNABLE_TO_CHECK_FOR_UPDATES_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/unable_to_check_for_updates_screen_screen/UNABLE_TO_CHECK_FOR_UPDATES_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/unable_to_check_for_updates_screen_screen/UNABLE_TO_CHECK_FOR_UPDATES_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/update_available_screen_screen/UPDATE_AVAILABLE_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/update_available_screen_screen/UPDATE_AVAILABLE_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/update_available_screen_screen/UPDATE_AVAILABLE_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/watch_is_updating_screen_screen/WATCH_IS_UPDATING_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/watch_is_updating_screen_screen/WATCH_IS_UPDATING_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/watch_is_updating_screen_screen/WATCH_IS_UPDATING_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/vibration_main_screen_screen/VIBRATION_MAIN_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/vibration_main_screen_screen/VIBRATION_MAIN_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/vibration_main_screen_screen/VIBRATION_MAIN_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/VIBRATION.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/VIBRATIONBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/ToggleButton.hpp:

../../TouchGFX/gui/include/gui/pedometer_main_screen_screen/PEDOMETER_MAIN_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/pedometer_main_screen_screen/PEDOMETER_MAIN_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/pedometer_main_screen_screen/PEDOMETER_MAIN_SCREENPresenter.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/progress_indicators/LineProgress.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/progress_indicators/AbstractProgressIndicator.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/canvas/Line.hpp:

../../TouchGFX/gui/include/gui/reference_screen/ReferenceView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/reference_screen/ReferenceViewBase.hpp:

../../TouchGFX/gui/include/gui/reference_screen/ReferencePresenter.hpp:

../../TouchGFX/gui/include/gui/pedometer_reset_goal_screen_screen/PEDOMETER_RESET_GOAL_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/pedometer_reset_goal_screen_screen/PEDOMETER_RESET_GOAL_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/pedometer_reset_goal_screen_screen/PEDOMETER_RESET_GOAL_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/pedometer_settings_screen_screen/PEDOMETER_SETTINGS_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/pedometer_settings_screen_screen/PEDOMETER_SETTINGS_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/pedometer_settings_screen_screen/PEDOMETER_SETTINGS_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/pedometer_set_goal_screen_screen/PEDOMETER_SET_GOAL_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/pedometer_set_goal_screen_screen/PEDOMETER_SET_GOAL_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/pedometer_set_goal_screen_screen/PEDOMETER_SET_GOAL_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/cbt_progress_screen_screen/CBT_Progress_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/cbt_progress_screen_screen/CBT_Progress_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/cbt_progress_screen_screen/CBT_Progress_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/temperature_normal_screen/Temperature_NormalView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/temperature_normal_screen/Temperature_NormalViewBase.hpp:

../../TouchGFX/gui/include/gui/temperature_normal_screen/Temperature_NormalPresenter.hpp:

../../TouchGFX/gui/include/gui/cbt_error_1_screen/CBT_ERROR_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/cbt_error_1_screen/CBT_ERROR_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/cbt_error_1_screen/CBT_ERROR_1Presenter.hpp:

../../TouchGFX/gui/include/gui/settins_screen_timeout_1_screen/SETTINS_SCREEN_TIMEOUT_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/settins_screen_timeout_1_screen/SETTINS_SCREEN_TIMEOUT_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/settins_screen_timeout_1_screen/SETTINS_SCREEN_TIMEOUT_1Presenter.hpp:

../../TouchGFX/gui/include/gui/settins_screen_timeout_2_screen/SETTINS_SCREEN_TIMEOUT_2View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/settins_screen_timeout_2_screen/SETTINS_SCREEN_TIMEOUT_2ViewBase.hpp:

../../TouchGFX/gui/include/gui/settins_screen_timeout_2_screen/SETTINS_SCREEN_TIMEOUT_2Presenter.hpp:

../../TouchGFX/gui/include/gui/containers/SCREEN_TIMEOUT.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/SCREEN_TIMEOUTBase.hpp:

../../TouchGFX/gui/include/gui/watch_face_1_screen/WATCH_FACE_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/watch_face_1_screen/WATCH_FACE_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/watch_face_1_screen/WATCH_FACE_1Presenter.hpp:

../../TouchGFX/gui/include/gui/WatchFacePopupBase.h:

../../TouchGFX/gui/include/gui/containers/WatchFacePopupContainer.hpp:

../../TouchGFX/gui/include/gui/settings_watch_face1_screen/SETTINGS_WATCH_FACE1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/settings_watch_face1_screen/SETTINGS_WATCH_FACE1ViewBase.hpp:

../../TouchGFX/gui/include/gui/settings_watch_face1_screen/SETTINGS_WATCH_FACE1Presenter.hpp:

../../TouchGFX/gui/include/gui/watch_face_done_screen/WATCH_FACE_DONEView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/watch_face_done_screen/WATCH_FACE_DONEViewBase.hpp:

../../TouchGFX/gui/include/gui/watch_face_done_screen/WATCH_FACE_DONEPresenter.hpp:

../../TouchGFX/gui/include/gui/home_passkey_reset_screen/HOME_PASSKEY_RESETView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/home_passkey_reset_screen/HOME_PASSKEY_RESETViewBase.hpp:

../../TouchGFX/gui/include/gui/home_passkey_reset_screen/HOME_PASSKEY_RESETPresenter.hpp:

../../TouchGFX/gui/include/gui/settings_passcode_enable_screen/SETTINGS_PASSCODE_ENABLEView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/settings_passcode_enable_screen/SETTINGS_PASSCODE_ENABLEViewBase.hpp:

../../TouchGFX/gui/include/gui/settings_passcode_enable_screen/SETTINGS_PASSCODE_ENABLEPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/PasscodeContainer.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/PasscodeContainerBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/ButtonWithLabel.hpp:

../../TouchGFX/gui/include/gui/bluetooth_turned_off_screen_screen/BLUETOOTH_TURNED_OFF_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/bluetooth_turned_off_screen_screen/BLUETOOTH_TURNED_OFF_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/bluetooth_turned_off_screen_screen/BLUETOOTH_TURNED_OFF_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/developer_mode_screen/DEVELOPER_MODEView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/developer_mode_screen/DEVELOPER_MODEViewBase.hpp:

../../TouchGFX/gui/include/gui/developer_mode_screen/DEVELOPER_MODEPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/DeveloperMode.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/DeveloperModeBase.hpp:

../../TouchGFX/gui/include/gui/wifi_flash_screen/WIFI_FLASHView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/wifi_flash_screen/WIFI_FLASHViewBase.hpp:

../../TouchGFX/gui/include/gui/wifi_flash_screen/WIFI_FLASHPresenter.hpp:

../../TouchGFX/gui/include/gui/vital_sleep_screen/VITAL_SLEEPView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/vital_sleep_screen/VITAL_SLEEPViewBase.hpp:

../../TouchGFX/gui/include/gui/vital_sleep_screen/VITAL_SLEEPPresenter.hpp:

../../TouchGFX/gui/include/gui/coredump_screen_screen/coredump_ScreenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/coredump_screen_screen/coredump_ScreenViewBase.hpp:

../../TouchGFX/gui/include/gui/coredump_screen_screen/coredump_ScreenPresenter.hpp:

../../TouchGFX/gui/include/gui/cbt_main_screen_1_screen/CBT_Main_Screen_1View.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/cbt_main_screen_1_screen/CBT_Main_Screen_1ViewBase.hpp:

../../TouchGFX/gui/include/gui/cbt_main_screen_1_screen/CBT_Main_Screen_1Presenter.hpp:

../../TouchGFX/gui/include/gui/cbt_history_celsius_fahrenhit_history_screen/CBT_HISTORY_CELSIUS_FAHRENHIT_HISTORYView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/cbt_history_celsius_fahrenhit_history_screen/CBT_HISTORY_CELSIUS_FAHRENHIT_HISTORYViewBase.hpp:

../../TouchGFX/gui/include/gui/cbt_history_celsius_fahrenhit_history_screen/CBT_HISTORY_CELSIUS_FAHRENHIT_HISTORYPresenter.hpp:

../../TouchGFX/gui/include/gui/calibration_screen/CALIBRATIONView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/calibration_screen/CALIBRATIONViewBase.hpp:

../../TouchGFX/gui/include/gui/calibration_screen/CALIBRATIONPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/CALIBRATION.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/CALIBRATIONBase.hpp:

../../TouchGFX/gui/include/gui/sos_main_screen/SOS_MAINView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/sos_main_screen/SOS_MAINViewBase.hpp:

../../TouchGFX/gui/include/gui/sos_main_screen/SOS_MAINPresenter.hpp:

../../TouchGFX/gui/include/gui/gsr_screen/GSRView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/gsr_screen/GSRViewBase.hpp:

../../TouchGFX/gui/include/gui/gsr_screen/GSRPresenter.hpp:

../../TouchGFX/gui/include/gui/settings_wifi_scanning_screen/SETTINGS_WIFI_SCANNINGView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/settings_wifi_scanning_screen/SETTINGS_WIFI_SCANNINGViewBase.hpp:

../../TouchGFX/gui/include/gui/settings_wifi_scanning_screen/SETTINGS_WIFI_SCANNINGPresenter.hpp:

../../TouchGFX/gui/include/gui/watchface_passcode_screen/WATCHFACE_PASSCODEView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/watchface_passcode_screen/WATCHFACE_PASSCODEViewBase.hpp:

../../TouchGFX/gui/include/gui/watchface_passcode_screen/WATCHFACE_PASSCODEPresenter.hpp:

../../TouchGFX/gui/include/gui/comm_state_screen_screen/COMM_STATE_SCREENView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/comm_state_screen_screen/COMM_STATE_SCREENViewBase.hpp:

../../TouchGFX/gui/include/gui/comm_state_screen_screen/COMM_STATE_SCREENPresenter.hpp:

../../TouchGFX/gui/include/gui/containers/COMMStateContainer.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/COMMStateContainerBase.hpp:

../../TouchGFX/gui/include/gui/spo2_max_screen/Spo2_MAXView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/spo2_max_screen/Spo2_MAXViewBase.hpp:

../../TouchGFX/gui/include/gui/spo2_max_screen/Spo2_MAXPresenter.hpp:
