max86176sample20230425.c:52:6:<PERSON><PERSON><PERSON>	24	static
max86176sample20230425.c:70:6:doI2C	40	static
max86176sample20230425.c:112:6:sleep	16	static
max86176sample20230425.c:127:6:reg<PERSON>rite	48	dynamic
max86176sample20230425.c:151:6:regR<PERSON>	48	dynamic
max86176sample20230425.c:174:6:MAX86176Init	16	static
max86176sample20230425.c:531:6:MAX86176<PERSON>hutdown	16	static
max86176sample20230425.c:570:6:MAX86176Wakeup	8	static
max86176sample20230425.c:702:6:MAX86176_ReadStatusRegisters	8	static
max86176sample20230425.c:723:9:MAX86176_GetFIFOWritePtr	24	static
max86176sample20230425.c:753:9:MAX86176_GetFIFOReadPtr	24	static
max86176sample20230425.c:783:6:MAX<PERSON>176_SetFIFOReadPtr	16	static
max86176sample20230425.c:809:9:MAX86176_GetFIFOOverflowCounter	24	static
max86176sample20230425.c:840:10:MAX86176_GetFIFODataCount	24	static
max86176sample20230425.c:876:6:MAX86176_ReadFIFOData	16	static
max86176sample20230425.c:905:6:MAX86176_SetFIFOThreshold	24	static
max86176sample20230425.c:934:6:MAX86176_SetFIFOMark	24	static
max86176sample20230425.c:964:6:MAX86176_FIFOFlush	24	static
max86176sample20230425.c:994:6:MAX86176_ConfigFIFOStatusClear	24	static
max86176sample20230425.c:1025:6:MAX86176_ConfigFIFOFullType	24	static
max86176sample20230425.c:1056:6:MAX86176_ControlFIFORollOver	24	static
max86176sample20230425.c:1091:6:MAX86176_FR_Clk_Sel	24	static
max86176sample20230425.c:1116:6:MAX86176_FR_Clk_Div	24	static
max86176sample20230425.c:1151:6:MAX86176_PD_Bias	24	static
max86176sample20230425.c:1182:6:MAX86176_Trig_ICFG	24	static
max86176sample20230425.c:1214:6:MAX86176_EnablePLL	24	static
max86176sample20230425.c:1246:6:MAX86176_LockDownPLL	24	static
max86176sample20230425.c:1284:6:MAX86176_SetMdiv	24	static
max86176sample20230425.c:1322:6:MAX86176_SetNdiv	24	static
max86176sample20230425.c:1359:6:MAX86176_Reset	24	static
max86176sample20230425.c:1392:6:MAX86176_Shutdown	24	static
max86176sample20230425.c:1423:6:MAX86176_PPG_PowerDown	24	static
max86176sample20230425.c:1454:6:MAX86176_SyncMode	24	static
max86176sample20230425.c:1483:6:MAX86176_SWForceSync	24	static
max86176sample20230425.c:1515:6:MAX86176_TimingSysReset	24	static
max86176sample20230425.c:1539:6:MAX86176_MeasurementEnable	24	static
max86176sample20230425.c:1577:6:MAX86176_Meas1ConfigSelection	24	static
max86176sample20230425.c:1608:6:MAX86176_CollectRawData	24	static
max86176sample20230425.c:1637:6:MAX86176_PPGTimingData	24	static
max86176sample20230425.c:1666:6:MAX86176_SetMaster	24	static
max86176sample20230425.c:1695:6:MAX86176_SetALCdisable	24	static
max86176sample20230425.c:1723:6:MAX86176_SetVDDoutOfRange	24	static
max86176sample20230425.c:1752:6:MAX86176_SetProximityAuto	24	static
max86176sample20230425.c:1781:6:MAX86176_SetProximityAutoEn	24	static
max86176sample20230425.c:1818:6:MAX86176_SetSampleSyncFrequency	24	static
max86176sample20230425.c:1851:6:MAX86176_SetIntegrationTimePPG	24	static
max86176sample20230425.c:1888:6:MAX86176_SetAverageValue	24	static
max86176sample20230425.c:1929:6:MAX86176_SetAdcRangePPG	24	static
max86176sample20230425.c:1970:6:MAX86176_PPGSelectPD	24	static
max86176sample20230425.c:2014:6:MAX86176_SetPPG_DACOFF	24	static
max86176sample20230425.c:2051:6:MAX86176_setLEDRange	24	static
max86176sample20230425.c:2090:6:MAX86176_PPG_Gain	24	static
max86176sample20230425.c:2126:6:MAX86176_ALC	24	static
max86176sample20230425.c:2161:6:MAX86176_PPGdecimationFilter	24	static
max86176sample20230425.c:2198:6:MAX86176_DirectAmbient	24	static
max86176sample20230425.c:2232:6:MAX86176_LED_DriveCurrentPA	24	static
max86176sample20230425.c:2270:6:MAX86176_LEDSetting	24	static
max86176sample20230425.c:2308:6:MAX86176_PDSetting	24	static
max86176sample20230425.c:2352:6:MAX86176_SetLEDDRiver	24	static
max86176sample20230425.c:2389:6:MAX86176_BufferChannel	24	static
max86176sample20230425.c:2437:6:MAX86176_Threshold_PPG_detect	24	static
max86176sample20230425.c:2465:6:MAX86176_Threshold_PPG_Select	24	static
max86176sample20230425.c:2496:6:MAX86176_TimeHysteresis	24	static
max86176sample20230425.c:2533:6:MAX86176_LevelHysteresis	24	static
max86176sample20230425.c:2567:6:MAX86176_SetThreshold	16	static
max86176sample20230425.c:2603:6:MAX86176_ECGEnable	16	static
max86176sample20230425.c:2625:6:MAX86176_ECGDisable	16	static
max86176sample20230425.c:2654:6:MAX86176_SetECGDecimationRate	24	static
max86176sample20230425.c:2683:6:MAX86176_SetECGPolarity	24	static
max86176sample20230425.c:2721:6:MAX86176_SetPGAGain	24	static
max86176sample20230425.c:2756:6:MAX86176_SetINAGain	24	static
max86176sample20230425.c:2800:6:MAX86176_SetECGFastRecovery	24	static
max86176sample20230425.c:2835:6:MAX86176_LeadOnDetection	24	static
max86176sample20230425.c:2868:6:MAX86176_LeadOffDetection	24	static
max86176sample20230425.c:2901:6:MAX86176_SetLeadOffStimulusMode	24	static
max86176sample20230425.c:2928:6:MAX86176_SetLeadOffSettle	24	static
max86176sample20230425.c:2957:6:MAX86176_ControlDCLeadOffRapid	24	static
max86176sample20230425.c:2984:6:MAX86176_SelectHiCommonModeImpedance	24	static
max86176sample20230425.c:3020:6:MAX86176_SetLeadOffCurrentGenerator	24	static
max86176sample20230425.c:3067:6:MAX86176_SetLeadOffCurrentMagnitude	24	static
max86176sample20230425.c:3097:6:MAX86176_SelectLeadOffCurrentWave	24	static
max86176sample20230425.c:3129:6:MAX86176_SetLeadOffCurrentFreq	24	static
max86176sample20230425.c:3164:6:MAX86176_SetLeadOffADCConversionRate	24	static
max86176sample20230425.c:3198:6:MAX86176_SetDCLeadOffCurrent	24	static
max86176sample20230425.c:3239:6:MAX86176_SelectLeadOffCGCapSize	24	static
max86176sample20230425.c:3273:6:MAX86176_SetLeadOffVoltageThreshold	24	static
max86176sample20230425.c:3309:6:MAX86176_SelectLeadOffCompareSignal	16	static
max86176sample20230425.c:3339:6:MAX86176_SetACLeadOffThreshold	16	static
max86176sample20230425.c:3365:6:MAX86176_SetLeadOffUtilPGAGain	24	static
max86176sample20230425.c:3404:6:MAX86176_SetLeadOffHPFCornerFreq	24	static
max86176sample20230425.c:3435:9:MAX86176_ReadLeadOffCaliResVariation	24	static
max86176sample20230425.c:3469:6:MAX86176_SetLeadBiasResistance	24	static
max86176sample20230425.c:3501:6:MAX86176_EnableLeadBiasP	24	static
max86176sample20230425.c:3530:6:MAX86176_EnableLeadBiasN	24	static
max86176sample20230425.c:3568:6:MAX86176_EnableECGCalibration	24	static
max86176sample20230425.c:3606:6:MAX86176_SetCalTimeHigh	24	static
max86176sample20230425.c:3651:6:MAX86176_ControlECGInputSwitch	24	static
max86176sample20230425.c:3684:6:MAX86176_SetCalibMode	24	static
max86176sample20230425.c:3728:6:MAX86176_SelectCalVoltageSource	24	static
max86176sample20230425.c:3779:6:MAX86176_ConfigureRLD	24	static
max86176sample20230425.c:3822:6:MAX86176_ConfigureRLDVoltageOutofRange	24	static
max86176sample20230425.c:3864:6:MAX86176_SetAveragerInput	24	static
max86176sample20230425.c:3901:6:MAX86176_SelectRLDExtRes	24	static
max86176sample20230425.c:3936:6:MAX86176_SelectCommonModeVoltage	24	static
max86176sample20230425.c:3965:8:MAX86176_SetRLDBandWidth	24	static
max86176sample20230425.c:4000:6:MAX86176_SetRLDBodyBiasDAC	24	static
max86176sample20230425.c:4061:6:MAX86176_SetUtilADCMux	24	static
max86176sample20230425.c:4094:6:MAX86176_UtilStartADC	24	static
max86176sample20230425.c:4117:10:MAX86176_ReadUtilADCData	24	static
max86176sample20230425.c:4148:6:MAX86176_WhoAmI	16	static
max86176sample20230425.c:4187:9:MAX86176_INT_EN1	24	static
max86176sample20230425.c:4227:6:MAX86176_Timing_Sys_Reset_EN	24	static
max86176sample20230425.c:4256:9:MAX86176_INT_EN2	24	static
max86176sample20230425.c:4303:9:MAX86176_INT_EN3	24	static
max86176sample20230425.c:4346:9:MAX86176_INT_EN4	24	static
max86176sample20230425.c:4392:9:MAX86176_INT_EN5	24	static
max86176sample20230425.c:4439:9:MAX86176_INT_EN6	24	static
max86176sample20230425.c:4488:6:MAX86176_INT_FCFG	24	static
max86176sample20230425.c:4520:6:MAX86176_INT_OCFG	24	static
