SerialInterface.c:52:17:_get_crc	32	static
SerialInterface.c:76:10:get_crc	40	static
SerialInterface.c:109:13:wakeupble	16	static
SerialInterface.c:118:13:wifi_timeout_trig	8	static
SerialInterface.c:125:13:wakeup<PERSON>fi	16	static
SerialInterface.c:134:6:wakeupWireless	16	static
SerialInterface.c:149:18:blestatus	8	static
SerialInterface.c:154:17:wifistatus	8	static
SerialInterface.c:159:10:WirelessStatus	8	static
SerialInterface.c:174:10:WirelessPortStatus	8	static
SerialInterface.c:190:13:dummyUartTx	16	static
SerialInterface.c:200:13:bleUartTx	16	static
SerialInterface.c:209:14:wifiUartTx	16	static
SerialInterface.c:214:13:WirelessUartTx	16	static
SerialInterface.c:223:13:InitDMABuffer	16	static
SerialInterface.c:231:6:HAL_UART_ErrorCallback	16	static
SerialInterface.c:241:16:Open_Uart	16	static
SerialInterface.c:252:13:Close_Uart	16	static
SerialInterface.c:259:6:Close_Rbuffer	8	static
SerialInterface.c:277:6:Open_Rbuffer	8	static
SerialInterface.c:295:9:rbuf_get	16	static
SerialInterface.c:303:6:Debug_DMA_Interrupt	8	static
SerialInterface.c:312:6:HAL_UARTEx_RxEventCallback	24	static
SerialInterface.c:336:13:BleBootTxcplt	24	static
SerialInterface.c:344:13:pendingDmaTx	24	static
SerialInterface.c:357:6:HAL_UART_TxCpltCallback	16	static
SerialInterface.c:373:6:updateWirelessModule	16	static
SerialInterface.c:392:10:getMtuSize	24	static
SerialInterface.c:425:16:getRadioType	4	static
SerialInterface.c:430:17:Radio_RST_State	8	static
SerialInterface.c:448:17:Radio_PWRON_State	8	static
SerialInterface.c:467:18:Radio_Sleep_State	16	static
SerialInterface.c:502:16:InitializeRadio	16	static
SerialInterface.c:527:10:Init_Wireless	16	static
SerialInterface.c:533:16:Start_RX	16	static
SerialInterface.c:553:13:Stop_RX	16	static
SerialInterface.c:560:6:Open_Master_tPort	8	static
SerialInterface.c:579:6:Open_Master_rPort	8	static
SerialInterface.c:589:6:Open_Slave_Port	8	static
SerialInterface.c:612:6:hClose_Port	8	static
SerialInterface.c:617:6:Close_Port	8	static
SerialInterface.c:640:6:Reset_Port	8	static
SerialInterface.c:664:6:Refresh_Port	8	static
SerialInterface.c:672:6:sendBreakCmd	8	static
SerialInterface.c:682:9:Com_BLE_ON	24	static
SerialInterface.c:692:9:Com_BLE_OFF	24	static
SerialInterface.c:702:9:Com_Wifi_ON	24	static
SerialInterface.c:712:9:Com_Wifi_OFF	24	static
SerialInterface.c:722:9:Com_BLE_Boot_Success	24	static
SerialInterface.c:732:6:COM_BLEWakeup	32	static
SerialInterface.c:750:6:COM_WiFiWakeup	32	static
SerialInterface.c:768:5:_platform_transmit	16	static
SerialInterface.c:775:5:_platform_break_handle	8	static
