# SPO2 BLE Transmission Diagnosis Guide

## Problem: SPO2 Data Not Transmitting via BLE

Based on code analysis, here are the potential issues and diagnostic steps:

## Key Findings

1. **MAX_SENSOR is ENABLED** (`MAX_SENSOR=1` in .cproject)
2. **SPO2 BLE transmission code EXISTS** in `MAX_SPO2.c` 
3. **BLE connection dependency** - transmission requires active BLE connection
4. **Debug code has been added** to help diagnose the issue

## Potential Root Causes

### 1. BLE Connection Issues
**Most Likely Cause**: BLE not connected when SPO2 data is ready to transmit.

**Check:**
- BLE initialization status
- BLE connection state
- BLE pairing/bonding status

### 2. SPO2 Algorithm Issues
**Possible Cause**: SPO2 algorithm not producing valid data.

**Check:**
- SPO2 sensor initialization
- Algorithm output values
- Data quality thresholds

### 3. Communication Manager Issues
**Possible Cause**: Communication queue full or transmission failure.

**Check:**
- Communication manager state
- Queue status
- Transmission return codes

## Diagnostic Steps

### Step 1: Check Debug Output
After building and running the modified code, check for these debug messages:

```
SPO2 Algorithm Output: spo2=XX, confidence=XX, quality=XX, r=XX
BLE Connection Status: CONNECTED/DISCONNECTED (comm_state=X)
SPO2 BLE Send Result: X (data_len=XX)
SPO2 BLE Data: SPO2: XX,Confidence: XX, quality : XX, r: XX
```

### Step 2: Verify BLE Connection
1. **Check BLE Status**: Look for "BLE Connection Status: CONNECTED"
2. **If DISCONNECTED**: 
   - Check BLE initialization in `BleOperations.c`
   - Verify BLE module power and GPIO configuration
   - Check for BLE boot errors

### Step 3: Verify SPO2 Data
1. **Check Algorithm Output**: Ensure spo2, confidence values are reasonable
2. **If Invalid Data**:
   - Check MAX86176 sensor initialization
   - Verify I2C communication with sensor
   - Check sensor placement/contact

### Step 4: Check Communication Flow
1. **Send Result**: Should return 1 for success, -1 for failure
2. **If Send Fails**:
   - Check communication manager initialization
   - Verify queue is not full
   - Check transport layer status

## Code Locations to Check

### BLE Connection Status
```c
// File: STM32CubeIDE/Application/User/AWT_COMM/Com_Task.c
int32_t isCommConnected(void)
{
    if(COMM_STATE == ble_connected || COMM_STATE == wifi_connected)
        return 1;
    else
        return 0;
}
```

### BLE Initialization
```c
// File: STM32CubeIDE/Application/User/AWT_COMM/BleOperations.c
void BleBootSuccess(void)
{
    UpdateBLEStatus(ble_on);
    Set_Com_State(S_BLE_ON);
    updateWirelessModule(Radio_BLE);
    setCommState(ble_on);
}
```

### SPO2 Transmission
```c
// File: STM32CubeIDE/Application/User/AWT_MAX/Src/MAX_SPO2.c
// Lines 231-255 (modified with debug code)
```

## Common Issues and Solutions

### Issue 1: BLE Not Connected
**Symptoms**: "BLE Connection Status: DISCONNECTED"
**Solutions**:
- Enable BLE from UI
- Check BLE module power supply
- Verify UART3 configuration
- Check BLE binary loading

### Issue 2: Invalid SPO2 Data
**Symptoms**: spo2=0, confidence=0
**Solutions**:
- Check sensor contact with skin
- Verify MAX86176 I2C communication
- Check LED current settings
- Verify algorithm initialization

### Issue 3: Communication Failure
**Symptoms**: "SPO2 BLE Send Result: -1"
**Solutions**:
- Check if BLE is connected before sending
- Verify communication manager is initialized
- Check queue capacity
- Verify subid `comm_wSpo2rawData` (0x0032) is valid

## Next Steps

1. **Build and run** the modified code with debug output
2. **Monitor debug messages** via UART/SWO/RTT
3. **Identify which stage is failing** based on debug output
4. **Apply specific fixes** based on the identified issue

## Additional Debug Options

### Enable More Debug Prints
Add these defines to enable more debugging:
```c
#define COMM_MANAGER_DEBUG 1
#define TRANSPORT_MANAGER_DEBUG 1
#define SPO2_DEBUG_PRINT 1
```

### Check BLE Status Manually
```c
// Add this to check BLE status
wireless_status_t ble_status = Get_Wireless_Status();
printf("BLE Wireless Status: %d\n", ble_status);
```

### Monitor Communication Queue
```c
// Check if communication queue is full
// Add debug in Com_Send_Data function
```

## Expected Normal Flow

1. SPO2 sensor reads data → Algorithm processes → Valid SPO2 values
2. BLE connection established → Communication manager ready
3. SPO2 data formatted → Com_Send_Data called → Data queued for transmission
4. Transport manager → Serial interface → BLE module → Transmitted

The debug code will help identify at which stage the process is failing.
