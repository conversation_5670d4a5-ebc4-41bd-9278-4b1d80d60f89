Application/User/AWT_COMM/Lib/Comm_Manager.o: \
 ../Application/User/AWT_COMM/Lib/Comm_Manager.c \
 ../Application/User/AWT_COMM/Lib/Comm_Manager.h \
 ../../Core/Inc/Awt_types.h ../../Core/Inc/Wireless.h \
 ../../Core/Inc/database.h ../../Core/Inc/Awt_types.h \
 ../../Core/Inc/watch_state.h ../../Core/Inc/AWT_UI_Wireless.h \
 D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/SerialInterface.h \
 ../Application/User/AWT_COMM/Lib/PrimitiveQueue.h \
 ../Application/User/AWT_COMM/Lib/TransportQueue.h \
 D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Comm_Debug.h \
 D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/UpperLayerInterface.h

../Application/User/AWT_COMM/Lib/Comm_Manager.h:

../../Core/Inc/Awt_types.h:

../../Core/Inc/Wireless.h:

../../Core/Inc/database.h:

../../Core/Inc/Awt_types.h:

../../Core/Inc/watch_state.h:

../../Core/Inc/AWT_UI_Wireless.h:

D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/SerialInterface.h:

../Application/User/AWT_COMM/Lib/PrimitiveQueue.h:

../Application/User/AWT_COMM/Lib/TransportQueue.h:

D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Comm_Debug.h:

D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/UpperLayerInterface.h:
