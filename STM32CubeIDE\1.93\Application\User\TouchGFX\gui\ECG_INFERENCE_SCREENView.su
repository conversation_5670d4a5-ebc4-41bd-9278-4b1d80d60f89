GestureEvent.hpp:74:22:touchgfx::GestureEvent::GestureEventType touchgfx::GestureEvent::getType() const	16	static
Drawable.hpp:62:13:touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:62:13:virtual touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:132:23:virtual touchgfx::Drawable* touchgfx::Drawable::getFirstChild()	16	static
Drawable.hpp:269:18:virtual void touchgfx::Drawable::setX(int16_t)	16	static
Drawable.hpp:281:18:virtual void touchgfx::Drawable::setY(int16_t)	16	static
Drawable.hpp:310:18:virtual void touchgfx::Drawable::setWidth(int16_t)	16	static
Drawable.hpp:322:18:virtual void touchgfx::Drawable::setHeight(int16_t)	16	static
Drawable.hpp:332:18:virtual void touchgfx::Drawable::childGeometryChanged()	16	static
Drawable.hpp:343:18:virtual void touchgfx::Drawable::handleClickEvent(const touchgfx::ClickEvent&)	16	static
Drawable.hpp:354:18:virtual void touchgfx::Drawable::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
Drawable.hpp:442:18:virtual void touchgfx::Drawable::handleDragEvent(const touchgfx::DragEvent&)	16	static
Drawable.hpp:452:18:virtual void touchgfx::Drawable::handleTickEvent()	16	static
Drawable.hpp:488:10:bool touchgfx::Drawable::isVisible() const	16	static
Drawable.hpp:500:10:bool touchgfx::Drawable::isTouchable() const	16	static
Drawable.hpp:539:18:virtual void touchgfx::Drawable::moveTo(int16_t, int16_t)	16	static
Drawable.hpp:576:10:void touchgfx::Drawable::resetDrawChainCache()	16	static
Drawable.hpp:654:18:virtual void touchgfx::Drawable::setupDrawChain(const touchgfx::Rect&, touchgfx::Drawable**)	24	static
WidgetStore.hpp:56:7:void UI::CtxStore::SetQuickSwitch(int)	16	static
WidgetStore.hpp:57:7:int UI::CtxStore::GetQuickSwitch()	16	static
Screen.hpp:47:13:touchgfx::Screen::~Screen()	16	static
Screen.hpp:47:13:virtual touchgfx::Screen::~Screen()	16	static
Screen.hpp:94:18:virtual void touchgfx::Screen::setupScreen()	16	static
Screen.hpp:105:18:virtual void touchgfx::Screen::afterTransition()	16	static
Screen.hpp:116:18:virtual void touchgfx::Screen::tearDownScreen()	16	static
Screen.hpp:144:18:virtual void touchgfx::Screen::handleTickEvent()	16	static
Screen.hpp:154:18:virtual void touchgfx::Screen::handleKeyEvent(uint8_t)	16	static
Screen.hpp:209:10:void touchgfx::Screen::addPopup(touchgfx::Container&)	16	static
Screen.hpp:219:10:void touchgfx::Screen::addStatusBar(touchgfx::Container&)	16	static
Screen.hpp:229:10:void touchgfx::Screen::addQuickmenu(touchgfx::Container&)	16	static
Widget.hpp:47:18:virtual void touchgfx::Widget::getLastChild(int16_t, int16_t, touchgfx::Drawable**)	24	static
Widget.hpp:36:7:touchgfx::Widget::~Widget()	16	static
Widget.hpp:36:7:virtual touchgfx::Widget::~Widget()	16	static
AbstractButton.hpp:32:7:touchgfx::AbstractButton::~AbstractButton()	16	static
AbstractButton.hpp:32:7:virtual touchgfx::AbstractButton::~AbstractButton()	16	static
TypedText.hpp:65:13:touchgfx::TypedText::~TypedText()	16	static
TypedText.hpp:65:13:virtual touchgfx::TypedText::~TypedText()	16	static
TextArea.hpp:39:7:touchgfx::TextArea::~TextArea()	16	static
TextArea.hpp:39:7:virtual touchgfx::TextArea::~TextArea()	16	static
Image.hpp:38:7:touchgfx::Image::~Image()	16	static
Image.hpp:38:7:virtual touchgfx::Image::~Image()	16	static
Container.hpp:40:7:touchgfx::Container::~Container()	16	static
Container.hpp:40:7:virtual touchgfx::Container::~Container()	16	static
PopupBase.h:21:2:PopupBase::PopupBase(touchgfx::Screen*)	16	static
PopupBase.h:26:10:PopupBase::~PopupBase()	16	static
PopupBase.h:26:10:virtual PopupBase::~PopupBase()	16	static
Battery_BT_containerBase.hpp:15:13:Battery_BT_containerBase::~Battery_BT_containerBase()	16	static
Battery_BT_containerBase.hpp:15:13:virtual Battery_BT_containerBase::~Battery_BT_containerBase()	16	static
Battery_BT_container.hpp:10:10:Battery_BT_container::~Battery_BT_container()	16	static
Battery_BT_container.hpp:10:10:virtual Battery_BT_container::~Battery_BT_container()	16	static
StatusBarBase.h:19:2:StatusbarBase::StatusbarBase(touchgfx::Screen*)	16	static
StatusBarBase.h:25:10:StatusbarBase::~StatusbarBase()	16	static
StatusBarBase.h:25:10:virtual StatusbarBase::~StatusbarBase()	16	static
Quick_accessContainerBase.hpp:23:13:Quick_accessContainerBase::~Quick_accessContainerBase()	16	static
Quick_accessContainerBase.hpp:23:13:virtual Quick_accessContainerBase::~Quick_accessContainerBase()	16	static
Quick_accessContainer.hpp:35:10:Quick_accessContainer::~Quick_accessContainer()	16	static
Quick_accessContainer.hpp:35:10:virtual Quick_accessContainer::~Quick_accessContainer()	16	static
QuickMenuBase.h:19:2:QuickMenuBase::QuickMenuBase(touchgfx::Screen*)	16	static
QuickMenuBase.h:25:10:QuickMenuBase::~QuickMenuBase()	16	static
QuickMenuBase.h:25:10:virtual QuickMenuBase::~QuickMenuBase()	16	static
ECG_INFERENCE_CONTAINERBase.hpp:17:13:ECG_INFERENCE_CONTAINERBase::~ECG_INFERENCE_CONTAINERBase()	16	static
ECG_INFERENCE_CONTAINERBase.hpp:17:13:virtual ECG_INFERENCE_CONTAINERBase::~ECG_INFERENCE_CONTAINERBase()	16	static
ECG_INFERENCE_CONTAINER.hpp:10:13:ECG_INFERENCE_CONTAINER::~ECG_INFERENCE_CONTAINER()	16	static
ECG_INFERENCE_CONTAINER.hpp:10:13:virtual ECG_INFERENCE_CONTAINER::~ECG_INFERENCE_CONTAINER()	16	static
View.hpp:36:7:touchgfx::View<ECG_INFERENCE_SCREENPresenter>::~View()	16	static
View.hpp:36:7:virtual touchgfx::View<ECG_INFERENCE_SCREENPresenter>::~View()	16	static
ECG_INFERENCE_SCREENViewBase.hpp:22:13:ECG_INFERENCE_SCREENViewBase::~ECG_INFERENCE_SCREENViewBase()	16	static
ECG_INFERENCE_SCREENViewBase.hpp:22:13:virtual ECG_INFERENCE_SCREENViewBase::~ECG_INFERENCE_SCREENViewBase()	16	static
ECG_INFERENCE_SCREENView.hpp:14:10:ECG_INFERENCE_SCREENView::~ECG_INFERENCE_SCREENView()	16	static
ECG_INFERENCE_SCREENView.hpp:14:10:virtual ECG_INFERENCE_SCREENView::~ECG_INFERENCE_SCREENView()	16	static
ECG_INFERENCE_SCREENView.cpp:8:10:static uint32_t ECG_INFERENCE_SCREENView::GetActive()	4	static
ECG_INFERENCE_SCREENView.cpp:12:1:ECG_INFERENCE_SCREENView::ECG_INFERENCE_SCREENView()	16	static
ECG_INFERENCE_SCREENView.cpp:17:6:virtual void ECG_INFERENCE_SCREENView::setupScreen()	16	static
ECG_INFERENCE_SCREENView.cpp:26:6:virtual void ECG_INFERENCE_SCREENView::tearDownScreen()	16	static
ECG_INFERENCE_SCREENView.cpp:37:6:virtual void ECG_INFERENCE_SCREENView::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
ECG_INFERENCE_SCREENView.cpp:44:6:virtual void ECG_INFERENCE_SCREENView::handleTickEvent()	16	static
Button.hpp:31:7:touchgfx::Button::~Button()	16	static
Button.hpp:31:7:virtual touchgfx::Button::~Button()	16	static
TextAreaWithWildcard.hpp:34:7:touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
TextAreaWithWildcard.hpp:34:7:virtual touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
Callback.hpp:357:8:touchgfx::Callback<Quick_accessContainerBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<Quick_accessContainerBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Box.hpp:29:7:touchgfx::Box::~Box()	16	static
Box.hpp:29:7:virtual touchgfx::Box::~Box()	16	static
ScrollableContainer.hpp:46:7:touchgfx::ScrollableContainer::~ScrollableContainer()	16	static
ScrollableContainer.hpp:46:7:virtual touchgfx::ScrollableContainer::~ScrollableContainer()	16	static
Callback.hpp:357:8:touchgfx::Callback<ECG_INFERENCE_SCREENViewBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<ECG_INFERENCE_SCREENViewBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = ECG_INFERENCE_SCREENViewBase; T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = ECG_INFERENCE_SCREENViewBase; T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = Quick_accessContainerBase; T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = Quick_accessContainerBase; T1 = const touchgfx::AbstractButton&]	16	static
