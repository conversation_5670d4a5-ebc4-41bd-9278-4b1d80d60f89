Application/User/TouchGFX/gui/Blood_OxygenView.o: \
 D:/spo2_zcross/max_zcrossing/TouchGFX/gui/src/blood_oxygen_screen/Blood_OxygenView.cpp \
 ../../TouchGFX/gui/include/gui/blood_oxygen_screen/Blood_OxygenView.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/blood_oxygen_screen/Blood_OxygenViewBase.hpp \
 ../../TouchGFX/gui/include/gui/common/FrontendApplication.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/common/FrontendApplicationBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/mvp/MVPApplication.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Types.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Config.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Application.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/UIEventListener.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/ClickEvent.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Event.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/DragEvent.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/GestureEvent.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/lcd/LCD.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Bitmap.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Font.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Unicode.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/TextProvider.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/TextureMapTypes.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/lcd/DebugPrinter.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Callback.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Screen.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/Container.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Drawable.hpp \
 ../../TouchGFX/store/WidgetStore.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/Transition.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/common/AbstractPartition.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/mvp/MVPHeap.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/mvp/Presenter.hpp \
 ../../TouchGFX/gui/include/gui/model/Model.hpp ../../Core/Inc/database.h \
 ../../Core/Inc/Awt_types.h ../../Core/Inc/watch_state.h \
 ../../Core/Inc/AWT_UI_Wireless.h \
 D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc/algohub_api.h \
 ../../Core/Boards/Inc/1.93/main.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h \
 ../../Core/Inc/stm32l4xx_hal_conf.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h \
 ../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h \
 ../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4r9xx.h \
 ../../Drivers/CMSIS/Include/core_cm4.h \
 ../../Drivers/CMSIS/Include/cmsis_version.h \
 ../../Drivers/CMSIS/Include/cmsis_compiler.h \
 ../../Drivers/CMSIS/Include/cmsis_gcc.h \
 ../../Drivers/CMSIS/Include/mpu_armv7.h \
 ../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_adc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_ll_adc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_adc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_crc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_crc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma2d.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dsi.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gfxmmu.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_lptim.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ltdc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ltdc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dsi.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ospi.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rtc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rtc_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_sram.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_ll_fmc.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h \
 ../../Core/Inc/ring_buf.h \
 ../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h \
 ../../Core/Inc/FreeRTOSConfig.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/task.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/list.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h \
 ../../Core/Inc/Awt_types.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/task.h \
 ../../Middlewares/ST/touchgfx/framework/include/mvp/View.hpp \
 ../../TouchGFX/gui/include/gui/blood_oxygen_screen/Blood_OxygenPresenter.hpp \
 ../../TouchGFX/gui/include/gui/model/ModelListener.hpp \
 ../../TouchGFX/gui/include/gui/PopupBase.h \
 ../../TouchGFX/gui/include/gui/containers/DefaultPopupContainer.hpp \
 ../../TouchGFX/gui/include/gui/containers/PopupContainer.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/PopupContainerBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/ModalWindow.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Box.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Widget.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Image.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Color.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Button.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/AbstractButton.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/TextAreaWithWildcard.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/TextArea.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/TypedText.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/Texts.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/Slider.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/AnimatedImage.hpp \
 ../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF/Awt_Vibrator.h \
 ../../Core/Inc/Flash_EEPROM.h ../../Core/Inc/database.h \
 ../../Core/Inc/AWT_UI_Wireless.h ../../Core/Inc/event_handler.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/semphr.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h \
 ../../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h \
 ../../Core/Inc/Flash_EEPROM.h ../../Core/Inc/Wireless.h \
 ../../Core/Inc/AWT_Boot_Option.h \
 ../../TouchGFX/gui/include/gui/StatusBarBase.h \
 ../../TouchGFX/gui/include/gui/containers/Battery_BT_container.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Battery_BT_containerBase.hpp \
 ../../TouchGFX/gui/include/gui/QuickMenuBase.h \
 ../../TouchGFX/gui/include/gui/containers/Quick_accessContainer.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Quick_accessContainerBase.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/SlideMenu.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/EasingEquations.hpp \
 ../../Middlewares/ST/touchgfx/framework/include/touchgfx/mixins/MoveAnimator.hpp \
 ../../TouchGFX/gui/include/gui/containers/SETTINGS_CHECKING_CONNECTIVITY.hpp \
 ../../TouchGFX/generated/gui_generated/include/gui_generated/containers/SETTINGS_CHECKING_CONNECTIVITYBase.hpp \
 ../../Core/Inc/abortState.h

../../TouchGFX/gui/include/gui/blood_oxygen_screen/Blood_OxygenView.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/blood_oxygen_screen/Blood_OxygenViewBase.hpp:

../../TouchGFX/gui/include/gui/common/FrontendApplication.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/common/FrontendApplicationBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/mvp/MVPApplication.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Types.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/hal/Config.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Application.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/UIEventListener.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/ClickEvent.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Event.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/DragEvent.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/events/GestureEvent.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/lcd/LCD.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Bitmap.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Font.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Unicode.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/TextProvider.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/TextureMapTypes.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/lcd/DebugPrinter.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Callback.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Screen.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/Container.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Drawable.hpp:

../../TouchGFX/store/WidgetStore.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/transitions/Transition.hpp:

../../Middlewares/ST/touchgfx/framework/include/common/AbstractPartition.hpp:

../../Middlewares/ST/touchgfx/framework/include/mvp/MVPHeap.hpp:

../../Middlewares/ST/touchgfx/framework/include/mvp/Presenter.hpp:

../../TouchGFX/gui/include/gui/model/Model.hpp:

../../Core/Inc/database.h:

../../Core/Inc/Awt_types.h:

../../Core/Inc/watch_state.h:

../../Core/Inc/AWT_UI_Wireless.h:

D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc/algohub_api.h:

../../Core/Boards/Inc/1.93/main.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal.h:

../../Core/Inc/stm32l4xx_hal_conf.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_def.h:

../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4xx.h:

../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/stm32l4r9xx.h:

../../Drivers/CMSIS/Include/core_cm4.h:

../../Drivers/CMSIS/Include/cmsis_version.h:

../../Drivers/CMSIS/Include/cmsis_compiler.h:

../../Drivers/CMSIS/Include/cmsis_gcc.h:

../../Drivers/CMSIS/Include/mpu_armv7.h:

../../Drivers/CMSIS/Device/ST/STM32L4xx/Include/system_stm32l4xx.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rcc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gpio_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_cortex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_adc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_ll_adc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_adc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_crc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_crc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dma2d.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dsi.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_exti.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_gfxmmu.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_flash_ramfunc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_i2c_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_lptim.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ltdc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ltdc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_dsi.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_ospi.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_pwr_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rtc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_rtc_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_sram.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_ll_fmc.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_tim_ex.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart.h:

../../Core/Inc/ring_buf.h:

../../Drivers/STM32L4xx_HAL_Driver/Inc/stm32l4xx_hal_uart_ex.h:

../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/FreeRTOS.h:

../../Core/Inc/FreeRTOSConfig.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/projdefs.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/portable.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/deprecated_definitions.h:

../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/portmacro.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/mpu_wrappers.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/task.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/list.h:

../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.h:

../../Core/Inc/Awt_types.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/task.h:

../../Middlewares/ST/touchgfx/framework/include/mvp/View.hpp:

../../TouchGFX/gui/include/gui/blood_oxygen_screen/Blood_OxygenPresenter.hpp:

../../TouchGFX/gui/include/gui/model/ModelListener.hpp:

../../TouchGFX/gui/include/gui/PopupBase.h:

../../TouchGFX/gui/include/gui/containers/DefaultPopupContainer.hpp:

../../TouchGFX/gui/include/gui/containers/PopupContainer.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/PopupContainerBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/ModalWindow.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Box.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Widget.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Image.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Color.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/Button.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/AbstractButton.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/TextAreaWithWildcard.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/TextArea.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/TypedText.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/Texts.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/Slider.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/widgets/AnimatedImage.hpp:

../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF/Awt_Vibrator.h:

../../Core/Inc/Flash_EEPROM.h:

../../Core/Inc/database.h:

../../Core/Inc/AWT_UI_Wireless.h:

../../Core/Inc/event_handler.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/event_groups.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/semphr.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/queue.h:

../../Middlewares/Third_Party/FreeRTOS/Source/include/timers.h:

../../Core/Inc/Flash_EEPROM.h:

../../Core/Inc/Wireless.h:

../../Core/Inc/AWT_Boot_Option.h:

../../TouchGFX/gui/include/gui/StatusBarBase.h:

../../TouchGFX/gui/include/gui/containers/Battery_BT_container.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Battery_BT_containerBase.hpp:

../../TouchGFX/gui/include/gui/QuickMenuBase.h:

../../TouchGFX/gui/include/gui/containers/Quick_accessContainer.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/Quick_accessContainerBase.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/containers/SlideMenu.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/EasingEquations.hpp:

../../Middlewares/ST/touchgfx/framework/include/touchgfx/mixins/MoveAnimator.hpp:

../../TouchGFX/gui/include/gui/containers/SETTINGS_CHECKING_CONNECTIVITY.hpp:

../../TouchGFX/generated/gui_generated/include/gui_generated/containers/SETTINGS_CHECKING_CONNECTIVITYBase.hpp:

../../Core/Inc/abortState.h:
