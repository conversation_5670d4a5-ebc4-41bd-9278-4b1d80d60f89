mfxstm32l152.c:150:6:mfxstm32l152_Init	24	static
mfxstm32l152.c:183:6:mfxstm32l152_DeInit	24	static
mfxstm32l152.c:203:6:mfxstm32l152_Reset	16	static
mfxstm32l152.c:217:7:mfxstm32l152_LowPower	16	static
mfxstm32l152.c:231:7:mfxstm32l152_WakeUp	24	static
mfxstm32l152.c:254:10:mfxstm32l152_ReadID	24	static
mfxstm32l152.c:275:10:mfxstm32l152_ReadFwVersion	24	static
mfxstm32l152.c:299:6:mfxstm32l152_EnableITSource	24	static
mfxstm32l152.c:327:6:mfxstm32l152_DisableITSource	24	static
mfxstm32l152.c:356:9:mfxstm32l152_GlobalITStatus	16	static
mfxstm32l152.c:378:6:mfxstm32l152_ClearGlobalIT	16	static
mfxstm32l152.c:392:6:mfxstm32l152_SetIrqOutPinPolarity	24	static
mfxstm32l152.c:421:6:mfxstm32l152_SetIrqOutPinType	24	static
mfxstm32l152.c:454:6:mfxstm32l152_IO_Start	24	static
mfxstm32l152.c:517:9:mfxstm32l152_IO_Config	24	static
mfxstm32l152.c:719:6:mfxstm32l152_IO_InitPin	16	static
mfxstm32l152.c:735:6:mfxstm32l152_IO_SetIrqEvtMode	16	static
mfxstm32l152.c:753:6:mfxstm32l152_IO_SetIrqTypeMode	16	static
mfxstm32l152.c:768:6:mfxstm32l152_IO_WritePin	16	static
mfxstm32l152.c:791:10:mfxstm32l152_IO_ReadPin	32	static
mfxstm32l152.c:820:6:mfxstm32l152_IO_EnableIT	16	static
mfxstm32l152.c:833:6:mfxstm32l152_IO_DisableIT	16	static
mfxstm32l152.c:847:6:mfxstm32l152_IO_EnablePinIT	16	static
mfxstm32l152.c:860:6:mfxstm32l152_IO_DisablePinIT	16	static
mfxstm32l152.c:873:10:mfxstm32l152_IO_ITStatus	24	static
mfxstm32l152.c:905:6:mfxstm32l152_IO_ClearIT	24	static
mfxstm32l152.c:937:6:mfxstm32l152_IO_EnableAF	24	static
mfxstm32l152.c:963:7:mfxstm32l152_IO_DisableAF	24	static
mfxstm32l152.c:995:6:mfxstm32l152_TS_Start	24	static
mfxstm32l152.c:1045:9:mfxstm32l152_TS_DetectTouch	24	static
mfxstm32l152.c:1071:6:mfxstm32l152_TS_GetXY	32	static
mfxstm32l152.c:1090:6:mfxstm32l152_TS_EnableIT	16	static
mfxstm32l152.c:1103:6:mfxstm32l152_TS_DisableIT	16	static
mfxstm32l152.c:1114:9:mfxstm32l152_TS_ITStatus	16	static
mfxstm32l152.c:1125:6:mfxstm32l152_TS_ClearIT	16	static
mfxstm32l152.c:1140:6:mfxstm32l152_IDD_Start	24	static
mfxstm32l152.c:1160:6:mfxstm32l152_IDD_Config	32	static
mfxstm32l152.c:1269:6:mfxstm32l152_IDD_ConfigShuntNbLimit	24	static
mfxstm32l152.c:1292:6:mfxstm32l152_IDD_GetValue	24	static
mfxstm32l152.c:1308:10:mfxstm32l152_IDD_GetShuntUsed	16	static
mfxstm32l152.c:1318:6:mfxstm32l152_IDD_EnableIT	16	static
mfxstm32l152.c:1331:6:mfxstm32l152_IDD_ClearIT	16	static
mfxstm32l152.c:1342:9:mfxstm32l152_IDD_GetITStatus	16	static
mfxstm32l152.c:1353:6:mfxstm32l152_IDD_DisableIT	16	static
mfxstm32l152.c:1369:9:mfxstm32l152_Error_ReadSrc	16	static
mfxstm32l152.c:1380:9:mfxstm32l152_Error_ReadMsg	16	static
mfxstm32l152.c:1392:6:mfxstm32l152_Error_EnableIT	16	static
mfxstm32l152.c:1405:6:mfxstm32l152_Error_ClearIT	16	static
mfxstm32l152.c:1416:9:mfxstm32l152_Error_GetITStatus	16	static
mfxstm32l152.c:1427:6:mfxstm32l152_Error_DisableIT	16	static
mfxstm32l152.c:1436:9:mfxstm32l152_ReadReg	16	static
mfxstm32l152.c:1442:6:mfxstm32l152_WriteReg	16	static
mfxstm32l152.c:1457:16:mfxstm32l152_GetInstance	24	static
mfxstm32l152.c:1478:16:mfxstm32l152_ReleaseInstance	24	static
mfxstm32l152.c:1502:6:mfxstm32l152_reg24_setPinValue	24	static
