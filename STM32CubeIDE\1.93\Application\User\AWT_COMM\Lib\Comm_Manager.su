Comm_Manager.c:19:6:print_comm_queue	16	static
Comm_Manager.c:40:6:COM_RXEvent	24	static
Comm_Manager.c:49:6:COM_TXCEvent	24	static
Comm_Manager.c:57:6:COM_BreakEvent	24	static
Comm_Manager.c:67:6:Command_Status	32	static
Comm_Manager.c:94:6:Response_Status	32	static
Comm_Manager.c:107:6:Indication_Status	32	static
Comm_Manager.c:120:6:<PERSON><PERSON>erxfer_Status	32	static
Comm_Manager.c:133:9:Com_Get_Data	40	static
Comm_Manager.c:156:9:Com_Send_Data	48	static
Comm_Manager.c:179:9:Com_<PERSON>uffer<PERSON><PERSON>	48	static
Comm_Manager.c:205:16:resetCommManager	8	static
Comm_Manager.c:213:17:Handle_Wireless_Trigger	16	static
Comm_Manager.c:250:17:Handle_Message	40	static
Comm_Manager.c:291:10:<PERSON><PERSON><PERSON><PERSON><PERSON>	48	static
Comm_Manager.c:333:6:WriteCmd	16	static
