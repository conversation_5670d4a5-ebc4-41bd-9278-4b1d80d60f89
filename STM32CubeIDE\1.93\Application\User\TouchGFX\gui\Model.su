Model.cpp:49:6:void Update_Model_Time()	4	static
Model.cpp:55:1:Model::Model()	16	static
Model.cpp:60:6:void Model::tick()	168	static
Model.cpp:365:6:void Initialise_Wireless_Status()	16	static
Model.cpp:402:6:void Model::UI_Up()	16	static
Model.cpp:414:6:void LiveConfigUpdate(uint8_t*)	40	static
Model.cpp:480:6:void Watch_Shutdown()	8	static
Model.cpp:488:1:void __static_initialization_and_destruction_0(int, int)	16	static
Model.cpp:488:1:cpp)	8	static
