event_groups.c:93:21:xEventGroupCreateStatic	32	static
event_groups.c:145:21:xEventGroupCreate	16	static
event_groups.c:191:13:xEventGroupSync	56	static,ignoring_inline_asm
event_groups.c:311:13:xEventGroupWaitBits	72	static,ignoring_inline_asm
event_groups.c:461:13:xEventGroupClearBits	32	static
event_groups.c:490:13:xEventGroupClearBitsFromISR	24	static
event_groups.c:503:13:xEventGroupGetBitsFromISR	40	static,ignoring_inline_asm
event_groups.c:519:13:xEventGroupSetBits	64	static
event_groups.c:613:6:vEventGroupDelete	32	static
event_groups.c:657:6:vEventGroupSetBitsCallback	16	static
event_groups.c:665:6:vEventGroupClearBitsCallback	16	static
event_groups.c:671:19:prvTestWaitCondition	32	static
event_groups.c:708:13:xEventGroupSetBitsFromISR	32	static
event_groups.c:723:14:uxEventGroupGetNumber	24	static
event_groups.c:745:7:vEventGroupSetNumber	16	static
