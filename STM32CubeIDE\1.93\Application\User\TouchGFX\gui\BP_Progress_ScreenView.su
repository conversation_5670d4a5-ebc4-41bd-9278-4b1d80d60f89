Types.hpp:140:5:touchgfx::Rect::Rect(int16_t, int16_t, int16_t, int16_t)	24	static
Bitmap.hpp:123:5:touchgfx::Bitmap::Bitmap(touchgfx::BitmapId)	16	static
Drawable.hpp:62:13:touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:62:13:virtual touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:132:23:virtual touchgfx::Drawable* touchgfx::Drawable::getFirstChild()	16	static
Drawable.hpp:269:18:virtual void touchgfx::Drawable::setX(int16_t)	16	static
Drawable.hpp:281:18:virtual void touchgfx::Drawable::setY(int16_t)	16	static
Drawable.hpp:297:10:void touchgfx::Drawable::setXY(int16_t, int16_t)	16	static
Drawable.hpp:310:18:virtual void touchgfx::Drawable::setWidth(int16_t)	16	static
Drawable.hpp:322:18:virtual void touchgfx::Drawable::setHeight(int16_t)	16	static
Drawable.hpp:332:18:virtual void touchgfx::Drawable::childGeometryChanged()	16	static
Drawable.hpp:343:18:virtual void touchgfx::Drawable::handleClickEvent(const touchgfx::ClickEvent&)	16	static
Drawable.hpp:354:18:virtual void touchgfx::Drawable::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
Drawable.hpp:442:18:virtual void touchgfx::Drawable::handleDragEvent(const touchgfx::DragEvent&)	16	static
Drawable.hpp:452:18:virtual void touchgfx::Drawable::handleTickEvent()	16	static
Drawable.hpp:466:10:void touchgfx::Drawable::setVisible(bool)	16	static
Drawable.hpp:488:10:bool touchgfx::Drawable::isVisible() const	16	static
Drawable.hpp:500:10:bool touchgfx::Drawable::isTouchable() const	16	static
Drawable.hpp:539:18:virtual void touchgfx::Drawable::moveTo(int16_t, int16_t)	16	static
Drawable.hpp:576:10:void touchgfx::Drawable::resetDrawChainCache()	16	static
Drawable.hpp:654:18:virtual void touchgfx::Drawable::setupDrawChain(const touchgfx::Rect&, touchgfx::Drawable**)	24	static
Screen.hpp:47:13:touchgfx::Screen::~Screen()	16	static
Screen.hpp:47:13:virtual touchgfx::Screen::~Screen()	16	static
Screen.hpp:94:18:virtual void touchgfx::Screen::setupScreen()	16	static
Screen.hpp:105:18:virtual void touchgfx::Screen::afterTransition()	16	static
Screen.hpp:116:18:virtual void touchgfx::Screen::tearDownScreen()	16	static
Screen.hpp:144:18:virtual void touchgfx::Screen::handleTickEvent()	16	static
Screen.hpp:154:18:virtual void touchgfx::Screen::handleKeyEvent(uint8_t)	16	static
Screen.hpp:209:10:void touchgfx::Screen::addPopup(touchgfx::Container&)	16	static
Screen.hpp:219:10:void touchgfx::Screen::addStatusBar(touchgfx::Container&)	16	static
Widget.hpp:47:18:virtual void touchgfx::Widget::getLastChild(int16_t, int16_t, touchgfx::Drawable**)	24	static
Widget.hpp:36:7:touchgfx::Widget::~Widget()	16	static
Widget.hpp:36:7:virtual touchgfx::Widget::~Widget()	16	static
AbstractButton.hpp:32:7:touchgfx::AbstractButton::~AbstractButton()	16	static
AbstractButton.hpp:32:7:virtual touchgfx::AbstractButton::~AbstractButton()	16	static
TypedText.hpp:65:13:touchgfx::TypedText::~TypedText()	16	static
TypedText.hpp:65:13:virtual touchgfx::TypedText::~TypedText()	16	static
TextArea.hpp:47:18:virtual touchgfx::Rect touchgfx::TextArea::getSolidRect() const	24	static
TextArea.hpp:76:10:void touchgfx::TextArea::setAlpha(uint8_t)	16	static
TextArea.hpp:98:18:virtual void touchgfx::TextArea::setBaselineY(int16_t)	48	static
TextArea.hpp:114:18:virtual void touchgfx::TextArea::setXBaselineY(int16_t, int16_t)	16	static
TypedText.hpp:41:7:constexpr touchgfx::TypedText::TypedText(const touchgfx::TypedText&)	16	static
TextArea.hpp:223:15:touchgfx::TypedText touchgfx::TextArea::getTypedText() const	16	static
TextArea.hpp:39:7:touchgfx::TextArea::~TextArea()	16	static
TextArea.hpp:39:7:virtual touchgfx::TextArea::~TextArea()	16	static
Image.hpp:38:7:touchgfx::Image::~Image()	16	static
Image.hpp:38:7:virtual touchgfx::Image::~Image()	16	static
Container.hpp:40:7:touchgfx::Container::~Container()	16	static
Container.hpp:40:7:virtual touchgfx::Container::~Container()	16	static
PopupBase.h:21:2:PopupBase::PopupBase(touchgfx::Screen*)	16	static
PopupBase.h:26:10:PopupBase::~PopupBase()	16	static
PopupBase.h:26:10:virtual PopupBase::~PopupBase()	16	static
Battery_BT_containerBase.hpp:15:13:Battery_BT_containerBase::~Battery_BT_containerBase()	16	static
Battery_BT_containerBase.hpp:15:13:virtual Battery_BT_containerBase::~Battery_BT_containerBase()	16	static
Battery_BT_container.hpp:10:10:Battery_BT_container::~Battery_BT_container()	16	static
Battery_BT_container.hpp:10:10:virtual Battery_BT_container::~Battery_BT_container()	16	static
StatusBarBase.h:19:2:StatusbarBase::StatusbarBase(touchgfx::Screen*)	16	static
StatusBarBase.h:25:10:StatusbarBase::~StatusbarBase()	16	static
StatusBarBase.h:25:10:virtual StatusbarBase::~StatusbarBase()	16	static
View.hpp:36:7:touchgfx::View<BP_Progress_ScreenPresenter>::~View()	16	static
View.hpp:36:7:virtual touchgfx::View<BP_Progress_ScreenPresenter>::~View()	16	static
BP_Progress_ScreenViewBase.hpp:26:13:BP_Progress_ScreenViewBase::~BP_Progress_ScreenViewBase()	16	static
BP_Progress_ScreenViewBase.hpp:26:13:virtual BP_Progress_ScreenViewBase::~BP_Progress_ScreenViewBase()	16	static
BP_Progress_ScreenViewBase.hpp:44:26:FrontendApplication& BP_Progress_ScreenViewBase::application()	16	static
BP_Progress_ScreenView.hpp:13:13:BP_Progress_ScreenView::~BP_Progress_ScreenView()	16	static
BP_Progress_ScreenView.hpp:13:13:virtual BP_Progress_ScreenView::~BP_Progress_ScreenView()	16	static
BP_Progress_ScreenView.cpp:7:10:static uint32_t BP_Progress_ScreenView::GetActive()	4	static
BP_Progress_ScreenView.cpp:12:1:BP_Progress_ScreenView::BP_Progress_ScreenView()	32	static
BP_Progress_ScreenView.cpp:43:6:virtual void BP_Progress_ScreenView::setupScreen()	16	static
BP_Progress_ScreenView.cpp:49:6:virtual void BP_Progress_ScreenView::tearDownScreen()	16	static
BP_Progress_ScreenView.cpp:55:6:virtual void BP_Progress_ScreenView::BP_DONE()	16	static
BP_Progress_ScreenView.cpp:61:6:virtual void BP_Progress_ScreenView::BPStart()	32	static
BP_Progress_ScreenView.cpp:96:6:virtual void BP_Progress_ScreenView::BPBackButton()	272	static
Button.hpp:31:7:touchgfx::Button::~Button()	16	static
Button.hpp:31:7:virtual touchgfx::Button::~Button()	16	static
AnimatedImage.hpp:33:7:touchgfx::AnimatedImage::~AnimatedImage()	16	static
AnimatedImage.hpp:33:7:virtual touchgfx::AnimatedImage::~AnimatedImage()	16	static
Box.hpp:29:7:touchgfx::Box::~Box()	16	static
Box.hpp:29:7:virtual touchgfx::Box::~Box()	16	static
FadeAnimator.hpp:41:7:touchgfx::FadeAnimator<touchgfx::TextArea>::~FadeAnimator()	16	static
FadeAnimator.hpp:41:7:virtual touchgfx::FadeAnimator<touchgfx::TextArea>::~FadeAnimator()	16	static
MoveAnimator.hpp:37:7:touchgfx::MoveAnimator<touchgfx::AnimatedImage>::~MoveAnimator()	16	static
MoveAnimator.hpp:37:7:virtual touchgfx::MoveAnimator<touchgfx::AnimatedImage>::~MoveAnimator()	16	static
MoveAnimator.hpp:37:7:touchgfx::MoveAnimator<touchgfx::Image>::~MoveAnimator()	16	static
MoveAnimator.hpp:37:7:virtual touchgfx::MoveAnimator<touchgfx::Image>::~MoveAnimator()	16	static
Callback.hpp:357:8:touchgfx::Callback<BP_Progress_ScreenViewBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<BP_Progress_ScreenViewBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:357:8:touchgfx::Callback<BP_Progress_ScreenViewBase, const touchgfx::FadeAnimator<touchgfx::TextArea>&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<BP_Progress_ScreenViewBase, const touchgfx::FadeAnimator<touchgfx::TextArea>&>::~Callback()	16	static
Callback.hpp:357:8:touchgfx::Callback<BP_Progress_ScreenViewBase, const touchgfx::MoveAnimator<touchgfx::Image>&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<BP_Progress_ScreenViewBase, const touchgfx::MoveAnimator<touchgfx::Image>&>::~Callback()	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::FadeAnimator<touchgfx::TextArea>&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::FadeAnimator<touchgfx::TextArea>&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::MoveAnimator<touchgfx::Image>&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::MoveAnimator<touchgfx::Image>&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = BP_Progress_ScreenViewBase; T1 = const touchgfx::FadeAnimator<touchgfx::TextArea>&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = BP_Progress_ScreenViewBase; T1 = const touchgfx::FadeAnimator<touchgfx::TextArea>&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = BP_Progress_ScreenViewBase; T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = BP_Progress_ScreenViewBase; T1 = const touchgfx::AbstractButton&]	16	static
MoveAnimator.hpp:169:18:void touchgfx::MoveAnimator<T>::handleTickEvent() [with T = touchgfx::Image]	16	static
MoveAnimator.hpp:88:18:void touchgfx::MoveAnimator<T>::setMoveAnimationDelay(uint16_t) [with T = touchgfx::Image]	16	static
MoveAnimator.hpp:100:22:uint16_t touchgfx::MoveAnimator<T>::getMoveAnimationDelay() const [with T = touchgfx::Image]	16	static
MoveAnimator.hpp:169:18:void touchgfx::MoveAnimator<T>::handleTickEvent() [with T = touchgfx::AnimatedImage]	16	static
MoveAnimator.hpp:88:18:void touchgfx::MoveAnimator<T>::setMoveAnimationDelay(uint16_t) [with T = touchgfx::AnimatedImage]	16	static
MoveAnimator.hpp:100:22:uint16_t touchgfx::MoveAnimator<T>::getMoveAnimationDelay() const [with T = touchgfx::AnimatedImage]	16	static
FadeAnimator.hpp:157:18:void touchgfx::FadeAnimator<T>::handleTickEvent() [with T = touchgfx::TextArea]	16	static
FadeAnimator.hpp:84:18:void touchgfx::FadeAnimator<T>::setFadeAnimationDelay(uint16_t) [with T = touchgfx::TextArea]	16	static
FadeAnimator.hpp:96:22:uint16_t touchgfx::FadeAnimator<T>::getFadeAnimationDelay() const [with T = touchgfx::TextArea]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = BP_Progress_ScreenViewBase; T1 = const touchgfx::MoveAnimator<touchgfx::Image>&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = BP_Progress_ScreenViewBase; T1 = const touchgfx::MoveAnimator<touchgfx::Image>&]	16	static
MoveAnimator.hpp:178:10:void touchgfx::MoveAnimator<T>::nextMoveAnimationStep() [with T = touchgfx::Image]	32	static
MoveAnimator.hpp:178:10:void touchgfx::MoveAnimator<T>::nextMoveAnimationStep() [with T = touchgfx::AnimatedImage]	32	static
FadeAnimator.hpp:165:10:void touchgfx::FadeAnimator<T>::nextFadeAnimationStep() [with T = touchgfx::TextArea]	32	static
