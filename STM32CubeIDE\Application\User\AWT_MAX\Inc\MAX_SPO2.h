/*
 * MAX_SPO2.h
 *
 *  Created on: 18-Jul-2024
 *
 */

/* Includes ------------------------------------------------------------------*/
#include "stm32l4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "stdbool.h"
#include "MAX86176.h"
#include <lis2ds12_reg.h>

#include "defs.h"

#include "algohub_api.h"
#include "algohub_config_api.h"
#include "AS7050_sensor.h"
/* USER CODE END Includes */

#define DUMMY_PPG_DATA      (8517u)
#define DUMMY_ACC_X_DATA    (0)
#define DUMMY_ACC_Y_DATA    (10u)
#define DUMMY_ACC_Z_DATA    (-800)

/*	Error States	*/
#define E_NO_ERROR			0x00
#define E_BAD_PARAM			0x01
#define E_BAD_STATE			0x02
#define E_NONE_AVAIL		0x03

#ifndef APPLICATION_USER_AWT_MAX_INC_MAX_SPO2_H_
#define APPLICATION_USER_AWT_MAX_INC_MAX_SPO2_H_

#include "database.h"

uint8_t MAX_SPO2(SENSOR_DATA_STRUCT *spo2, sensor_callback SPO2_Callback);
uint8_t MAX_SPO2_Processing(SENSOR_DATA_STRUCT *spo2, sensor_callback SPO2_Callback);
uint8_t MAX_WaitForSPO2Samples(SENSOR_DATA_STRUCT *spo2,sensor_callback SPO2_Callback);
uint8_t MAX_SPO2_Start();
uint8_t MAX_SPO2_Execute_Algorithm(SENSOR_DATA_STRUCT *vital);
uint8_t MAX86176_SPO2_Config(void);
uint8_t MAX_SPO2_INIT(void);
uint8_t MAX86176_SPO2_Init(void);
void Test_SPO2_BLE_Transmission(void);

#endif /* APPLICATION_USER_AWT_MAX_INC_MAX_SPO2_H_ */
