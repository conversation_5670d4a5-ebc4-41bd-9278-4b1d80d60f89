################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (9-2020-q2-update)
################################################################################

-include ../makefile.init

RM := rm -rf

# All of the sources participating in the build are defined here
-include sources.mk
-include Middlewares/FreeRTOS/subdir.mk
-include Drivers/STM32L4xx_HAL_Driver/subdir.mk
-include Drivers/CMSIS/subdir.mk
-include Drivers/BSP/STM32L4R9I-DISCOVERY/subdir.mk
-include Drivers/BSP/Components/subdir.mk
-include Application/User/TouchGFX/target/generated/subdir.mk
-include Application/User/TouchGFX/target/subdir.mk
-include Application/User/TouchGFX/gui/subdir.mk
-include Application/User/TouchGFX/generated/subdir.mk
-include Application/User/TouchGFX/Framework/Src/touchgfx/widgets/graph/subdir.mk
-include Application/User/TouchGFX/Framework/Src/touchgfx/widgets/canvas/subdir.mk
-include Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
-include Application/User/TouchGFX/Framework/Src/touchgfx/containers/scrollers/subdir.mk
-include Application/User/TouchGFX/Framework/Src/touchgfx/containers/progress_indicators/subdir.mk
-include Application/User/TouchGFX/Framework/Src/touchgfx/containers/clock/subdir.mk
-include Application/User/TouchGFX/Framework/Src/touchgfx/containers/subdir.mk
-include Application/User/TouchGFX/App/subdir.mk
-include Application/User/Store/subdir.mk
-include Application/User/Ringbuffer/subdir.mk
-include Application/User/AWT_WIFI_BOOT/Src/subdir.mk
-include Application/User/AWT_TACTILE_NOTIF/subdir.mk
-include Application/User/AWT_SYSTEM/subdir.mk
-include Application/User/AWT_SOS/subdir.mk
-include Application/User/AWT_SENSORS_DATA/subdir.mk
-include Application/User/AWT_SCH/subdir.mk
-include Application/User/AWT_RTOS_TASKS/subdir.mk
-include Application/User/AWT_PMIC/subdir.mk
-include Application/User/AWT_MAX/Src/subdir.mk
-include Application/User/AWT_FILTERS/subdir.mk
-include Application/User/AWT_EXTRA_DEBUG_LIB/subdir.mk
-include Application/User/AWT_DEBUG/subdir.mk
-include Application/User/AWT_DA14531_BLE/subdir.mk
-include Application/User/AWT_COMM/Lib/subdir.mk
-include Application/User/AWT_COMM/subdir.mk
-include Application/User/AWT_BUS/subdir.mk
-include Application/User/AWT_ABORT/subdir.mk
-include Application/Startup/subdir.mk
-include Application/Boards/Src/1.93/subdir.mk
-include subdir.mk
-include objects.mk

ifneq ($(MAKECMDGOALS),clean)
ifneq ($(strip $(CC_DEPS)),)
-include $(CC_DEPS)
endif
ifneq ($(strip $(C++_DEPS)),)
-include $(C++_DEPS)
endif
ifneq ($(strip $(C_UPPER_DEPS)),)
-include $(C_UPPER_DEPS)
endif
ifneq ($(strip $(CXX_DEPS)),)
-include $(CXX_DEPS)
endif
ifneq ($(strip $(S_DEPS)),)
-include $(S_DEPS)
endif
ifneq ($(strip $(S_UPPER_DEPS)),)
-include $(S_UPPER_DEPS)
endif
ifneq ($(strip $(C_DEPS)),)
-include $(C_DEPS)
endif
ifneq ($(strip $(CPP_DEPS)),)
-include $(CPP_DEPS)
endif
endif

-include ../makefile.defs

OPTIONAL_TOOL_DEPS := \
$(wildcard ../makefile.defs) \
$(wildcard ../makefile.init) \
$(wildcard ../makefile.targets) \


BUILD_ARTIFACT_NAME := STM32L4R9I_DISCOVERY
BUILD_ARTIFACT_EXTENSION := elf
BUILD_ARTIFACT_PREFIX :=
BUILD_ARTIFACT := $(BUILD_ARTIFACT_PREFIX)$(BUILD_ARTIFACT_NAME)$(if $(BUILD_ARTIFACT_EXTENSION),.$(BUILD_ARTIFACT_EXTENSION),)

# Add inputs and outputs from these tool invocations to the build variables 
EXECUTABLES += \
STM32L4R9I_DISCOVERY.elf \

SIZE_OUTPUT += \
default.size.stdout \


# All Target
all: main-build

# Main-build Target
main-build: STM32L4R9I_DISCOVERY.elf secondary-outputs

# Tool invocations
STM32L4R9I_DISCOVERY.elf: $(OBJS) $(USER_OBJS) D:\spo2_zcross\max_zcrossing\STM32CubeIDE\STM32L4R9AIIX_FLASH.ld makefile objects.list $(OPTIONAL_TOOL_DEPS)
	arm-none-eabi-g++ -o "STM32L4R9I_DISCOVERY.elf" @"objects.list" $(USER_OBJS) $(LIBS) -mcpu=cortex-m4 -T"D:\spo2_zcross\max_zcrossing\STM32CubeIDE\STM32L4R9AIIX_FLASH.ld" --specs=nosys.specs -Wl,-Map="STM32L4R9I_DISCOVERY.map" -Wl,--gc-sections -static -L../../Middlewares/ST/touchgfx/lib/core/cortex_m4f/gcc -L"../..\Drivers\CMSIS\Lib\GCC" -L../../STM32CubeIDE/Application/User/AWT_CORE_BODY_TEMP -L../../STM32CubeIDE/Drivers/CMSIS/Lib/GCC -L../../STM32CubeIDE/Application/User/AWT_AS7050/bio-app-handler/lib/hrm_a0/generic_armv7e-m-fpv4-sp-d16-fvp -L../../STM32CubeIDE/Application/User/AWT_AS7050/bio-app-handler/lib/spo2_a0/generic_armv7e-m-fpv4-sp-d16-fvp -u _printf_float --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -Wl,--start-group -lc -lm -lstdc++ -lsupc++ -Wl,--end-group
	@echo 'Finished building target: $@'
	@echo ' '

default.size.stdout: $(EXECUTABLES) makefile objects.list $(OPTIONAL_TOOL_DEPS)
	arm-none-eabi-size  $(EXECUTABLES)
	@echo 'Finished building: $@'
	@echo ' '

# Other Targets
clean:
	-$(RM) $(CC_DEPS)$(SIZE_OUTPUT)$(C++_DEPS)$(EXECUTABLES)$(OBJS)$(C_UPPER_DEPS)$(CXX_DEPS)$(S_DEPS)$(S_UPPER_DEPS)$(C_DEPS)$(CPP_DEPS) STM32L4R9I_DISCOVERY.elf
	-@echo ' '

secondary-outputs: $(SIZE_OUTPUT)

fail-specified-linker-script-missing:
	@echo 'Error: Cannot find the specified linker script. Check the linker settings in the build configuration.'
	@exit 2

warn-no-linker-script-specified:
	@echo 'Warning: No linker script specified. Check the linker settings in the build configuration.'

.PHONY: all clean dependents fail-specified-linker-script-missing warn-no-linker-script-specified

-include ../makefile.targets
