################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (9-2020-q2-update)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
CPP_SRCS += \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/AbstractButton.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/AnimatedImage.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/AnimationTextureMapper.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Box.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/BoxWithBorder.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Button.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/ButtonWithIcon.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/ButtonWithLabel.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Gauge.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Image.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Keyboard.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/PixelDataWidget.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/RadioButton.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/RepeatButton.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/ScalableImage.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/SnapshotWidget.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TextArea.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TextAreaWithWildcard.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TextureMapper.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TiledImage.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/ToggleButton.cpp \
D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TouchArea.cpp 

OBJS += \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/AbstractButton.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/AnimatedImage.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/AnimationTextureMapper.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Box.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/BoxWithBorder.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Button.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ButtonWithIcon.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ButtonWithLabel.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Gauge.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Image.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Keyboard.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/PixelDataWidget.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/RadioButton.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/RepeatButton.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ScalableImage.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/SnapshotWidget.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TextArea.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TextAreaWithWildcard.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TextureMapper.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TiledImage.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ToggleButton.o \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TouchArea.o 

CPP_DEPS += \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/AbstractButton.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/AnimatedImage.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/AnimationTextureMapper.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Box.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/BoxWithBorder.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Button.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ButtonWithIcon.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ButtonWithLabel.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Gauge.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Image.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Keyboard.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/PixelDataWidget.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/RadioButton.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/RepeatButton.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ScalableImage.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/SnapshotWidget.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TextArea.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TextAreaWithWildcard.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TextureMapper.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TiledImage.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ToggleButton.d \
./Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TouchArea.d 


# Each subdirectory must supply rules for building sources it contributes
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/AbstractButton.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/AbstractButton.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/AnimatedImage.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/AnimatedImage.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/AnimationTextureMapper.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/AnimationTextureMapper.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Box.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Box.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/BoxWithBorder.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/BoxWithBorder.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Button.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Button.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ButtonWithIcon.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/ButtonWithIcon.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ButtonWithLabel.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/ButtonWithLabel.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Gauge.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Gauge.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Image.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Image.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/Keyboard.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/Keyboard.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/PixelDataWidget.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/PixelDataWidget.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/RadioButton.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/RadioButton.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/RepeatButton.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/RepeatButton.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ScalableImage.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/ScalableImage.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/SnapshotWidget.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/SnapshotWidget.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TextArea.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TextArea.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TextAreaWithWildcard.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TextAreaWithWildcard.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TextureMapper.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TextureMapper.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TiledImage.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TiledImage.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/ToggleButton.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/ToggleButton.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"
Application/User/TouchGFX/Framework/Src/touchgfx/widgets/TouchArea.o: D:/spo2_zcross/max_zcrossing/Middlewares/ST/touchgfx/framework/source/touchgfx/widgets/TouchArea.cpp Application/User/TouchGFX/Framework/Src/touchgfx/widgets/subdir.mk
	arm-none-eabi-g++ "$<" -mcpu=cortex-m4 -std=gnu++14 -g3 -DSTM32L4R9xx '-DBOARD_1V92 =1' -DUSE_HAL_DRIVER -DDEBUG -DDEV_MODE=0 -DRAW_DATA=0 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DBOARD_1p75=0 -DAUTOMATION=0 -DDISABLE_WIFI=1 -DSETUP_FLOW=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I../../Core/Inc -I../../Core/Boards/Inc/1.93 -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../TouchGFX/store -I../../STM32CubeIDE/Application/User/AWT_DA16200_WiFi/Inc -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_SOS -I../../STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM/Lib" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -fno-exceptions -fno-rtti -fno-use-cxa-atexit -Wall -femit-class-debug-always -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

