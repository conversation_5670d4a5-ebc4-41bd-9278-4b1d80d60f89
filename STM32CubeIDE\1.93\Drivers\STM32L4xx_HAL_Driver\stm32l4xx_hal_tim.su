stm32l4xx_hal_tim.c:269:19:HAL_TIM_Base_Init	16	static
stm32l4xx_hal_tim.c:328:19:HAL_TIM_Base_DeInit	16	static
stm32l4xx_hal_tim.c:371:13:HAL_TIM_Base_MspInit	16	static
stm32l4xx_hal_tim.c:386:13:HAL_TIM_Base_MspDeInit	16	static
stm32l4xx_hal_tim.c:402:19:HAL_TIM_Base_Start	24	static
stm32l4xx_hal_tim.c:441:19:HAL_TIM_Base_Stop	16	static
stm32l4xx_hal_tim.c:461:19:HAL_TIM_Base_Start_IT	24	static
stm32l4xx_hal_tim.c:503:19:HAL_TIM_Base_Stop_IT	16	static
stm32l4xx_hal_tim.c:528:19:<PERSON><PERSON>_TIM_Base_Start_DMA	32	static
stm32l4xx_hal_tim.c:595:19:HAL_TIM_Base_Stop_DMA	16	static
stm32l4xx_hal_tim.c:650:19:HAL_TIM_OC_Init	16	static
stm32l4xx_hal_tim.c:709:19:HAL_TIM_OC_DeInit	16	static
stm32l4xx_hal_tim.c:752:13:HAL_TIM_OC_MspInit	16	static
stm32l4xx_hal_tim.c:767:13:HAL_TIM_OC_MspDeInit	16	static
stm32l4xx_hal_tim.c:790:19:HAL_TIM_OC_Start	24	static
stm32l4xx_hal_tim.c:846:19:HAL_TIM_OC_Stop	16	static
stm32l4xx_hal_tim.c:881:19:HAL_TIM_OC_Start_IT	24	static
stm32l4xx_hal_tim.c:969:19:HAL_TIM_OC_Stop_IT	16	static
stm32l4xx_hal_tim.c:1040:19:HAL_TIM_OC_Start_DMA	32	static
stm32l4xx_hal_tim.c:1190:19:HAL_TIM_OC_Stop_DMA	16	static
stm32l4xx_hal_tim.c:1287:19:HAL_TIM_PWM_Init	16	static
stm32l4xx_hal_tim.c:1346:19:HAL_TIM_PWM_DeInit	16	static
stm32l4xx_hal_tim.c:1389:13:HAL_TIM_PWM_MspInit	16	static
stm32l4xx_hal_tim.c:1404:13:HAL_TIM_PWM_MspDeInit	16	static
stm32l4xx_hal_tim.c:1427:19:HAL_TIM_PWM_Start	24	static
stm32l4xx_hal_tim.c:1483:19:HAL_TIM_PWM_Stop	16	static
stm32l4xx_hal_tim.c:1518:19:HAL_TIM_PWM_Start_IT	24	static
stm32l4xx_hal_tim.c:1605:19:HAL_TIM_PWM_Stop_IT	16	static
stm32l4xx_hal_tim.c:1676:19:HAL_TIM_PWM_Start_DMA	32	static
stm32l4xx_hal_tim.c:1825:19:HAL_TIM_PWM_Stop_DMA	16	static
stm32l4xx_hal_tim.c:1922:19:HAL_TIM_IC_Init	16	static
stm32l4xx_hal_tim.c:1981:19:HAL_TIM_IC_DeInit	16	static
stm32l4xx_hal_tim.c:2024:13:HAL_TIM_IC_MspInit	16	static
stm32l4xx_hal_tim.c:2039:13:HAL_TIM_IC_MspDeInit	16	static
stm32l4xx_hal_tim.c:2060:19:HAL_TIM_IC_Start	24	static
stm32l4xx_hal_tim.c:2112:19:HAL_TIM_IC_Stop	16	static
stm32l4xx_hal_tim.c:2142:19:HAL_TIM_IC_Start_IT	24	static
stm32l4xx_hal_tim.c:2227:19:HAL_TIM_IC_Stop_IT	16	static
stm32l4xx_hal_tim.c:2293:19:HAL_TIM_IC_Start_DMA	32	static
stm32l4xx_hal_tim.c:2441:19:HAL_TIM_IC_Stop_DMA	16	static
stm32l4xx_hal_tim.c:2540:19:HAL_TIM_OnePulse_Init	16	static
stm32l4xx_hal_tim.c:2608:19:HAL_TIM_OnePulse_DeInit	16	static
stm32l4xx_hal_tim.c:2653:13:HAL_TIM_OnePulse_MspInit	16	static
stm32l4xx_hal_tim.c:2668:13:HAL_TIM_OnePulse_MspDeInit	16	static
stm32l4xx_hal_tim.c:2687:19:HAL_TIM_OnePulse_Start	24	static
stm32l4xx_hal_tim.c:2743:19:HAL_TIM_OnePulse_Stop	16	static
stm32l4xx_hal_tim.c:2785:19:HAL_TIM_OnePulse_Start_IT	24	static
stm32l4xx_hal_tim.c:2847:19:HAL_TIM_OnePulse_Stop_IT	16	static
stm32l4xx_hal_tim.c:2926:19:HAL_TIM_Encoder_Init	32	static
stm32l4xx_hal_tim.c:3040:19:HAL_TIM_Encoder_DeInit	16	static
stm32l4xx_hal_tim.c:3085:13:HAL_TIM_Encoder_MspInit	16	static
stm32l4xx_hal_tim.c:3100:13:HAL_TIM_Encoder_MspDeInit	16	static
stm32l4xx_hal_tim.c:3120:19:HAL_TIM_Encoder_Start	24	static
stm32l4xx_hal_tim.c:3214:19:HAL_TIM_Encoder_Stop	16	static
stm32l4xx_hal_tim.c:3274:19:HAL_TIM_Encoder_Start_IT	24	static
stm32l4xx_hal_tim.c:3374:19:HAL_TIM_Encoder_Stop_IT	16	static
stm32l4xx_hal_tim.c:3439:19:HAL_TIM_Encoder_Start_DMA	32	static
stm32l4xx_hal_tim.c:3643:19:HAL_TIM_Encoder_Stop_DMA	16	static
stm32l4xx_hal_tim.c:3720:6:HAL_TIM_IRQHandler	16	static
stm32l4xx_hal_tim.c:3950:19:HAL_TIM_OC_ConfigChannel	24	static
stm32l4xx_hal_tim.c:4046:19:HAL_TIM_IC_ConfigChannel	24	static
stm32l4xx_hal_tim.c:4141:19:HAL_TIM_PWM_ConfigChannel	24	static
stm32l4xx_hal_tim.c:4286:19:HAL_TIM_OnePulse_ConfigChannel	56	static
stm32l4xx_hal_tim.c:4431:19:HAL_TIM_DMABurst_WriteStart	32	static
stm32l4xx_hal_tim.c:4483:19:HAL_TIM_DMABurst_MultiWriteStart	24	static
stm32l4xx_hal_tim.c:4653:19:HAL_TIM_DMABurst_WriteStop	24	static
stm32l4xx_hal_tim.c:4758:19:HAL_TIM_DMABurst_ReadStart	32	static
stm32l4xx_hal_tim.c:4810:19:HAL_TIM_DMABurst_MultiReadStart	24	static
stm32l4xx_hal_tim.c:4981:19:HAL_TIM_DMABurst_ReadStop	24	static
stm32l4xx_hal_tim.c:5063:19:HAL_TIM_GenerateEvent	16	static
stm32l4xx_hal_tim.c:5102:19:HAL_TIM_ConfigOCrefClear	24	static
stm32l4xx_hal_tim.c:5263:19:HAL_TIM_ConfigClockSource	24	static
stm32l4xx_hal_tim.c:5415:19:HAL_TIM_ConfigTI1Input	24	static
stm32l4xx_hal_tim.c:5447:19:HAL_TIM_SlaveConfigSynchro	16	static
stm32l4xx_hal_tim.c:5487:19:HAL_TIM_SlaveConfigSynchro_IT	16	static
stm32l4xx_hal_tim.c:5530:10:HAL_TIM_ReadCapturedValue	24	static
stm32l4xx_hal_tim.c:5614:13:HAL_TIM_PeriodElapsedCallback	16	static
stm32l4xx_hal_tim.c:5629:13:HAL_TIM_PeriodElapsedHalfCpltCallback	16	static
stm32l4xx_hal_tim.c:5644:13:HAL_TIM_OC_DelayElapsedCallback	16	static
stm32l4xx_hal_tim.c:5659:13:HAL_TIM_IC_CaptureCallback	16	static
stm32l4xx_hal_tim.c:5674:13:HAL_TIM_IC_CaptureHalfCpltCallback	16	static
stm32l4xx_hal_tim.c:5689:13:HAL_TIM_PWM_PulseFinishedCallback	16	static
stm32l4xx_hal_tim.c:5704:13:HAL_TIM_PWM_PulseFinishedHalfCpltCallback	16	static
stm32l4xx_hal_tim.c:5719:13:HAL_TIM_TriggerCallback	16	static
stm32l4xx_hal_tim.c:5734:13:HAL_TIM_TriggerHalfCpltCallback	16	static
stm32l4xx_hal_tim.c:5749:13:HAL_TIM_ErrorCallback	16	static
stm32l4xx_hal_tim.c:6276:22:HAL_TIM_Base_GetState	16	static
stm32l4xx_hal_tim.c:6286:22:HAL_TIM_OC_GetState	16	static
stm32l4xx_hal_tim.c:6296:22:HAL_TIM_PWM_GetState	16	static
stm32l4xx_hal_tim.c:6306:22:HAL_TIM_IC_GetState	16	static
stm32l4xx_hal_tim.c:6316:22:HAL_TIM_OnePulse_GetState	16	static
stm32l4xx_hal_tim.c:6326:22:HAL_TIM_Encoder_GetState	16	static
stm32l4xx_hal_tim.c:6336:23:HAL_TIM_GetActiveChannel	16	static
stm32l4xx_hal_tim.c:6354:29:HAL_TIM_GetChannelState	24	static
stm32l4xx_hal_tim.c:6371:30:HAL_TIM_DMABurstState	16	static
stm32l4xx_hal_tim.c:6396:6:TIM_DMAError	24	static
stm32l4xx_hal_tim.c:6439:6:TIM_DMADelayPulseCplt	24	static
stm32l4xx_hal_tim.c:6498:6:TIM_DMADelayPulseHalfCplt	24	static
stm32l4xx_hal_tim.c:6537:6:TIM_DMACaptureCplt	24	static
stm32l4xx_hal_tim.c:6600:6:TIM_DMACaptureHalfCplt	24	static
stm32l4xx_hal_tim.c:6639:13:TIM_DMAPeriodElapsedCplt	24	static
stm32l4xx_hal_tim.c:6660:13:TIM_DMAPeriodElapsedHalfCplt	24	static
stm32l4xx_hal_tim.c:6676:13:TIM_DMATriggerCplt	24	static
stm32l4xx_hal_tim.c:6697:13:TIM_DMATriggerHalfCplt	24	static
stm32l4xx_hal_tim.c:6714:6:TIM_Base_SetConfig	24	static
stm32l4xx_hal_tim.c:6762:13:TIM_OC1_SetConfig	32	static
stm32l4xx_hal_tim.c:6837:6:TIM_OC2_SetConfig	32	static
stm32l4xx_hal_tim.c:6913:13:TIM_OC3_SetConfig	32	static
stm32l4xx_hal_tim.c:6987:13:TIM_OC4_SetConfig	32	static
stm32l4xx_hal_tim.c:7047:13:TIM_OC5_SetConfig	32	static
stm32l4xx_hal_tim.c:7100:13:TIM_OC6_SetConfig	32	static
stm32l4xx_hal_tim.c:7154:26:TIM_SlaveTimer_SetConfig	32	static
stm32l4xx_hal_tim.c:7285:6:TIM_TI1_SetConfig	32	static
stm32l4xx_hal_tim.c:7332:13:TIM_TI1_ConfigInputStage	32	static
stm32l4xx_hal_tim.c:7375:13:TIM_TI2_SetConfig	32	static
stm32l4xx_hal_tim.c:7415:13:TIM_TI2_ConfigInputStage	32	static
stm32l4xx_hal_tim.c:7458:13:TIM_TI3_SetConfig	32	static
stm32l4xx_hal_tim.c:7506:13:TIM_TI4_SetConfig	32	static
stm32l4xx_hal_tim.c:7549:13:TIM_ITRx_SetConfig	24	static
stm32l4xx_hal_tim.c:7579:6:TIM_ETR_SetConfig	32	static
stm32l4xx_hal_tim.c:7611:6:TIM_CCxChannelCmd	32	static
