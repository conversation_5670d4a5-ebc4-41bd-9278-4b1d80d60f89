Types.hpp:127:5:touchgfx::Rect::Rect()	16	static
Gestures.hpp:36:9:touchgfx::Gestures::DragState::DragState()	16	static
Gestures.hpp:60:5:touchgfx::Gestures::Gestures()	16	static
MCUInstrumentation.hpp:30:5:touchgfx::MCUInstrumentation::MCUInstrumentation()	16	static
MCUInstrumentation.hpp:40:13:touchgfx::MCUInstrumentation::~MCUInstrumentation()	16	static
MCUInstrumentation.hpp:40:13:virtual touchgfx::MCUInstrumentation::~MCUInstrumentation()	16	static
MCUInstrumentation.hpp:67:18:virtual void touchgfx::MCUInstrumentation::setMCUActive(bool)	24	static
MCUInstrumentation.hpp:85:22:virtual uint32_t touchgfx::MCUInstrumentation::getCCConsumed()	16	static
MCUInstrumentation.hpp:95:18:virtual void touchgfx::MCUInstrumentation::setCCConsumed(uint32_t)	16	static
HAL.hpp:61:5:touchgfx::HAL::HAL(touchgfx::DMA_Interface&, touchgfx::LCD&, touchgfx::TouchController&, uint16_t, uint16_t)	24	static
HAL.hpp:109:13:touchgfx::HAL::~HAL()	16	static
HAL.hpp:109:13:virtual touchgfx::HAL::~HAL()	16	static
HAL.hpp:118:17:static touchgfx::HAL* touchgfx::HAL::getInstance()	4	static
HAL.hpp:132:18:virtual void touchgfx::HAL::setDisplayOrientation(touchgfx::DisplayOrientation)	16	static
HAL.hpp:202:10:void touchgfx::HAL::frontPorchEntered()	16	static
HAL.hpp:291:28:virtual touchgfx::BlitOperations touchgfx::HAL::getBlitCaps()	16	static
HAL.hpp:575:10:void touchgfx::HAL::vSync()	16	static
HAL.hpp:585:18:virtual void touchgfx::HAL::backPorchExited()	16	static
HAL.hpp:631:18:virtual bool touchgfx::HAL::sampleKey(uint8_t&)	16	static
HAL.hpp:747:10:void touchgfx::HAL::setMCUInstrumentation(touchgfx::MCUInstrumentation*)	16	static
HAL.hpp:758:10:void touchgfx::HAL::enableMCULoadCalculation(bool)	16	static
HAL.hpp:821:30:virtual touchgfx::FlashDataReader* touchgfx::HAL::getFlashDataReader() const	16	static
HAL.hpp:970:18:virtual void touchgfx::HAL::taskDelay(uint16_t)	16	static
HAL.hpp:997:22:virtual uint16_t touchgfx::HAL::getTFTCurrentLine()	16	static
HAL.hpp:1008:21:virtual touchgfx::DMAType touchgfx::HAL::getDMAType()	16	static
HAL.hpp:1118:18:virtual void touchgfx::HAL::performDisplayOrientationChange()	16	static
HAL.hpp:1147:18:virtual void touchgfx::HAL::InvalidateCache()	16	static
HAL.hpp:1157:18:virtual void touchgfx::HAL::FlushCache()	16	static
TouchGFXGeneratedHAL.hpp:44:5:TouchGFXGeneratedHAL::TouchGFXGeneratedHAL(touchgfx::DMA_Interface&, touchgfx::LCD&, touchgfx::TouchController&, uint16_t, uint16_t)	32	static
TouchGFXGeneratedHAL.hpp:104:18:virtual void TouchGFXGeneratedHAL::flushFrameBuffer()	16	static
TouchGFXHAL.hpp:108:18:virtual void TouchGFXHAL::flushFrameBuffer()	16	static
core_cm4.h:1679:22:void __NVIC_EnableIRQ(IRQn_Type)	16	static,ignoring_inline_asm
core_cm4.h:1717:22:void __NVIC_DisableIRQ(IRQn_Type)	16	static,ignoring_inline_asm
core_cm4.h:1809:22:void __NVIC_SetPriority(IRQn_Type, uint32_t)	16	static
CortexMMCUInstrumentation.hpp:20:7:touchgfx::CortexMMCUInstrumentation::CortexMMCUInstrumentation()	16	static
TouchGFXGeneratedHAL.hpp:28:7:TouchGFXGeneratedHAL::~TouchGFXGeneratedHAL()	16	static
TouchGFXGeneratedHAL.hpp:28:7:virtual TouchGFXGeneratedHAL::~TouchGFXGeneratedHAL()	16	static
TouchGFXHAL.cpp:106:1:TouchGFXHAL::TouchGFXHAL(touchgfx::DMA_Interface&, touchgfx::LCD&, touchgfx::TouchController&, uint16_t, uint16_t)	32	static
TouchGFXHAL.cpp:120:6:virtual void TouchGFXHAL::initialize()	24	static
TouchGFXHAL.cpp:148:11:virtual uint16_t* TouchGFXHAL::getTFTFrameBuffer() const	16	static
TouchGFXHAL.cpp:153:6:virtual void TouchGFXHAL::setTFTFrameBuffer(uint16_t*)	16	static
TouchGFXHAL.cpp:181:6:virtual void TouchGFXHAL::flushFrameBuffer(const touchgfx::Rect&)	16	static
TouchGFXHAL.cpp:195:6:virtual void TouchGFXHAL::configureInterrupts()	16	static
TouchGFXHAL.cpp:203:6:virtual void TouchGFXHAL::enableLCDControllerInterrupt()	16	static
TouchGFXHAL.cpp:212:6:virtual void TouchGFXHAL::disableInterrupts()	16	static
TouchGFXHAL.cpp:218:6:virtual void TouchGFXHAL::enableInterrupts()	16	static
TouchGFXHAL.cpp:225:6:virtual void TouchGFXHAL::setFrameBufferStartAddresses(void*, void*, void*)	32	static
TouchGFXHAL.cpp:259:6:virtual bool TouchGFXHAL::beginFrame()	16	static
TouchGFXHAL.cpp:264:6:virtual void TouchGFXHAL::endFrame()	16	static
TouchGFXHAL.cpp:271:10:void LCD_ReqTear()	24	static
TouchGFXHAL.cpp:285:10:void LCD_SetUpdateRegion(int)	32	static
TouchGFXHAL.cpp:297:10:void HAL_DSI_TearingEffectCallback(DSI_HandleTypeDef*)	16	static
TouchGFXHAL.cpp:312:10:void HAL_DSI_EndOfRefreshCallback(DSI_HandleTypeDef*)	16	static
TouchGFXHAL.cpp:328:10:void HAL_DSI_ErrorCallback(DSI_HandleTypeDef*)	16	static
TouchGFXHAL.cpp:335:19:long int IdleTaskHook(void*)	16	static
TouchGFXHAL.hpp:35:7:TouchGFXHAL::~TouchGFXHAL()	16	static
TouchGFXHAL.hpp:35:7:virtual TouchGFXHAL::~TouchGFXHAL()	16	static
CortexMMCUInstrumentation.hpp:20:7:touchgfx::CortexMMCUInstrumentation::~CortexMMCUInstrumentation()	16	static
CortexMMCUInstrumentation.hpp:20:7:virtual touchgfx::CortexMMCUInstrumentation::~CortexMMCUInstrumentation()	16	static
TouchGFXHAL.cpp:347:1:void __static_initialization_and_destruction_0(int, int)	16	static
TouchGFXHAL.cpp:347:1:cpp)	8	static
TouchGFXHAL.cpp:347:1:cpp)	8	static
