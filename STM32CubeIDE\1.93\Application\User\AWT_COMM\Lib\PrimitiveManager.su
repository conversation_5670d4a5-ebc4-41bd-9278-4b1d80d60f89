PrimitiveManager.c:216:12:cmd_tx_event	8	static
PrimitiveManager.c:225:12:cmd_f_event	8	static
PrimitiveManager.c:234:12:cmd_s_event	8	static
PrimitiveManager.c:244:12:resp_r_event	8	static
PrimitiveManager.c:258:12:cr_ack_s_event	8	static
PrimitiveManager.c:271:12:cr_nack_s_event	8	static
PrimitiveManager.c:285:12:cmd_r_event	16	static
PrimitiveManager.c:306:12:rsp_s_event	8	static
PrimitiveManager.c:315:12:rsp_f_event	8	static
PrimitiveManager.c:324:12:ind_tx_event	8	static
PrimitiveManager.c:333:12:ind_r_event	8	static
PrimitiveManager.c:347:12:ind_s_event	8	static
PrimitiveManager.c:356:12:ind_f_event	8	static
PrimitiveManager.c:365:12:ind_ack_s_event	8	static
PrimitiveManager.c:374:12:ind_nack_s_event	8	static
PrimitiveManager.c:383:12:cr_exec_s_event	8	static
PrimitiveManager.c:399:12:cr_exec_f_event	8	static
PrimitiveManager.c:414:12:ind_exec_s_event	8	static
PrimitiveManager.c:425:12:ind_exec_f_event	8	static
PrimitiveManager.c:437:12:ind_transp_f_event	8	static
PrimitiveManager.c:443:12:bufferxfer_event	8	static
PrimitiveManager.c:452:12:bufferxfer_rx_event	8	static
PrimitiveManager.c:461:12:bufferxfer_s_event	8	static
PrimitiveManager.c:470:12:bufferxfer_f_event	8	static
PrimitiveManager.c:479:12:bufferxfer_exec_s_event	8	static
PrimitiveManager.c:491:12:bufferxfer_exec_f_event	8	static
PrimitiveManager.c:503:12:handle_ind_change_state_event	16	static
PrimitiveManager.c:510:12:handle_ind_timeout_event	8	static
PrimitiveManager.c:515:12:handle_invalid_event	4	static
PrimitiveManager.c:520:12:cr_transp_f_event	8	static
PrimitiveManager.c:525:12:handle_cr_change_state_event	16	static
PrimitiveManager.c:532:12:handle_cr_timeout_event	8	static
PrimitiveManager.c:537:12:handle_p_cr_timeout	8	static
PrimitiveManager.c:565:12:handle_p_ind_timeout	8	static
PrimitiveManager.c:582:12:handle_p_cr_idle	8	static
PrimitiveManager.c:618:12:handle_p_ind_idle	8	static
PrimitiveManager.c:661:12:handle_p_csend	8	static
PrimitiveManager.c:674:12:handle_p_csuccess	4	static
PrimitiveManager.c:680:12:handle_p_rrecv	8	static
PrimitiveManager.c:687:12:handle_p_rexec	8	static
PrimitiveManager.c:709:12:handle_p_crecv	8	static
PrimitiveManager.c:729:12:handle_p_cexec	8	static
PrimitiveManager.c:762:12:handle_p_rsend	16	static
PrimitiveManager.c:779:12:handle_p_isend	16	static
PrimitiveManager.c:800:12:handle_p_irecv	8	static
PrimitiveManager.c:819:12:handle_p_iexec	8	static
PrimitiveManager.c:851:12:handle_p_bufferxfer	16	static
PrimitiveManager.c:872:12:handle_p_bufferxfer_rx	16	static
PrimitiveManager.c:913:12:handle_p_exec_bufferxfer	8	static
PrimitiveManager.c:941:12:handle_p_cr_msgfail	8	static
PrimitiveManager.c:952:12:handle_p_ind_msgfail	8	static
PrimitiveManager.c:963:12:handle_p_cr_transpfail	8	static
PrimitiveManager.c:1002:12:handle_p_ind_transpfail	8	static
PrimitiveManager.c:1019:12:handle_p_ind_invalid	4	static
PrimitiveManager.c:1024:12:handle_p_cr_invalid	4	static
PrimitiveManager.c:1029:12:p_cr_changeState	16	static
PrimitiveManager.c:1039:12:p_ind_changeState	16	static
PrimitiveManager.c:1049:6:CopyTrigger	16	static
PrimitiveManager.c:1074:20:UpdateTrigger	16	static
PrimitiveManager.c:1080:10:PrimitiveMain	24	static
PrimitiveManager.c:1116:9:UpdatePrimitiveTriggerRetry	16	static
PrimitiveManager.c:1131:6:SetPrimitiveTrigger_Data	32	static
PrimitiveManager.c:1172:6:SetPrimitiveTrigger_UL	40	static
PrimitiveManager.c:1199:6:SetPrimitiveTrigger_RT	32	static
PrimitiveManager.c:1230:10:GetPrimitiveTrigger	24	static
PrimitiveManager.c:1277:12:IsCR	16	static
PrimitiveManager.c:1303:12:IsInd	16	static
PrimitiveManager.c:1330:5:isPrimitiveIdle	4	static
PrimitiveManager.c:1339:9:resetPrimitiveManagerCR	8	static
PrimitiveManager.c:1372:9:resetPrimitiveManagerInd	8	static
PrimitiveManager.c:1400:9:timeoutPrimitiveManager	16	static
PrimitiveManager.c:1434:9:resetPrimitiveManager	8	static
PrimitiveManager.c:1442:9:resetPrimitiveMaster	16	static
PrimitiveManager.c:1457:13:HandleAppCallback	16	static
PrimitiveManager.c:1493:9:CurState_CR	4	static
PrimitiveManager.c:1498:9:PrevState_CR	4	static
PrimitiveManager.c:1503:9:CurState_I	4	static
PrimitiveManager.c:1508:9:PrevState_I	4	static
