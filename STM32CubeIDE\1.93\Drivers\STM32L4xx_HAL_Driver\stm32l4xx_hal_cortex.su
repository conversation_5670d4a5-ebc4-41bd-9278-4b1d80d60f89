core_cm4.h:1648:22:__NVIC_SetPriorityGrouping	24	static
core_cm4.h:1667:26:__NVIC_GetPriorityGrouping	4	static
core_cm4.h:1679:22:__NVIC_EnableIRQ	16	static,ignoring_inline_asm
core_cm4.h:1717:22:__NVIC_DisableIRQ	16	static,ignoring_inline_asm
core_cm4.h:1736:26:__NVIC_GetPendingIRQ	16	static
core_cm4.h:1755:22:__NVIC_SetPendingIRQ	16	static
core_cm4.h:1770:22:__NVIC_ClearPendingIRQ	16	static
core_cm4.h:1787:26:__NVIC_GetActive	16	static
core_cm4.h:1809:22:__NVIC_SetPriority	16	static
core_cm4.h:1831:26:__NVIC_GetPriority	16	static
core_cm4.h:1856:26:NVIC_EncodePriority	40	static
core_cm4.h:1883:22:NVIC_DecodePriority	40	static
core_cm4.h:1933:34:__NVIC_SystemReset	4	static,ignoring_inline_asm
core_cm4.h:2017:26:SysTick_Config	16	static
stm32l4xx_hal_cortex.c:165:6:HAL_NVIC_SetPriorityGrouping	16	static
stm32l4xx_hal_cortex.c:187:6:HAL_NVIC_SetPriority	32	static
stm32l4xx_hal_cortex.c:209:6:HAL_NVIC_EnableIRQ	16	static
stm32l4xx_hal_cortex.c:225:6:HAL_NVIC_DisableIRQ	16	static
stm32l4xx_hal_cortex.c:238:6:HAL_NVIC_SystemReset	8	static
stm32l4xx_hal_cortex.c:262:10:HAL_SYSTICK_Config	16	static
stm32l4xx_hal_cortex.c:290:10:HAL_NVIC_GetPriorityGrouping	8	static
stm32l4xx_hal_cortex.c:317:6:HAL_NVIC_GetPriority	24	static
stm32l4xx_hal_cortex.c:332:6:HAL_NVIC_SetPendingIRQ	16	static
stm32l4xx_hal_cortex.c:350:10:HAL_NVIC_GetPendingIRQ	16	static
stm32l4xx_hal_cortex.c:366:6:HAL_NVIC_ClearPendingIRQ	16	static
stm32l4xx_hal_cortex.c:383:10:HAL_NVIC_GetActive	16	static
stm32l4xx_hal_cortex.c:397:6:HAL_SYSTICK_CLKSourceConfig	16	static
stm32l4xx_hal_cortex.c:415:6:HAL_SYSTICK_IRQHandler	8	static
stm32l4xx_hal_cortex.c:424:13:HAL_SYSTICK_Callback	4	static
stm32l4xx_hal_cortex.c:443:6:HAL_MPU_Enable	16	static,ignoring_inline_asm
stm32l4xx_hal_cortex.c:458:6:HAL_MPU_Disable	4	static,ignoring_inline_asm
stm32l4xx_hal_cortex.c:474:6:HAL_MPU_ConfigRegion	16	static
