stm32l4xx_hal_uart_ex.c:170:19:HAL_RS485Ex_Init	32	static
stm32l4xx_hal_uart_ex.c:276:13:HAL_UARTEx_WakeupCallback	16	static
stm32l4xx_hal_uart_ex.c:292:13:HAL_UARTEx_RxFifoFullCallback	16	static
stm32l4xx_hal_uart_ex.c:307:13:HAL_UARTEx_TxFifoEmptyCallback	16	static
stm32l4xx_hal_uart_ex.c:439:19:HAL_MultiProcessorEx_AddressLength_Set	16	static
stm32l4xx_hal_uart_ex.c:477:19:HAL_UARTEx_StopModeWakeUpSourceConfig	40	static
stm32l4xx_hal_uart_ex.c:532:19:HAL_UARTEx_EnableStopMode	40	static,ignoring_inline_asm
stm32l4xx_hal_uart_ex.c:551:19:HAL_UARTEx_DisableStopMode	40	static,ignoring_inline_asm
stm32l4xx_hal_uart_ex.c:571:19:HAL_UARTEx_EnableFifoMode	24	static
stm32l4xx_hal_uart_ex.c:612:19:HAL_UARTEx_DisableFifoMode	24	static
stm32l4xx_hal_uart_ex.c:658:19:HAL_UARTEx_SetTxFifoThreshold	24	static
stm32l4xx_hal_uart_ex.c:707:19:HAL_UARTEx_SetRxFifoThreshold	24	static
stm32l4xx_hal_uart_ex.c:765:19:HAL_UARTEx_ReceiveToIdle	40	static
stm32l4xx_hal_uart_ex.c:890:19:HAL_UARTEx_ReceiveToIdle_IT	56	static,ignoring_inline_asm
stm32l4xx_hal_uart_ex.c:952:19:HAL_UARTEx_ReceiveToIdle_DMA	56	static,ignoring_inline_asm
stm32l4xx_hal_uart_ex.c:1021:13:UARTEx_Wakeup_AddressConfig	24	static
stm32l4xx_hal_uart_ex.c:1040:13:UARTEx_SetNbDataToProcess	24	static
