/*
 * MAX_SPO2.c
 *
 *  Created on: 18-Jul-2024
 *
 */

#include <MAX_Algorithm.h>
#include "MAX_SPO2.h"
#include "MAX86176.h"
#include "error_codes.h"
#include "std_inc.h"
#include "Awt_types.h"
#include "AWT_SENSORS_DATA.h"

#define SPO2_Reading_Timeout 		10000
#define SPO2_SWV					0
#define SPO2_CONFIDENCE				100

static err_code_t result = ERR_SUCCESS;

int16_t accln[3];
lis2ds12_status_t acclstatus;

uint16_t LED_Current;
uint32_t SPO2_AFE_Count = 0;
uint8_t afereqs[20];
algohub_feed_data_t algohub_in;
algohub_report_t algohub_out;
bool ppgsampleflag;
extern bool gUseEcg, gUseEcgPpgTime;
extern UART_HandleTypeDef hlpuart1;

extern int32_t adcCountArr[NUM_ADC][NUM_SAMPLES_PER_INT*EXTRABUFFER];
uint32_t Green1, Green2, <PERSON>, Ir;
extern uint8_t PPG_Frame_Cnt;
uint32_t Spo2_Threshold = 36;
uint32_t Spo2_SWV_Threshold = 5;


uint8_t MAX_SPO2(SENSOR_DATA_STRUCT *spo2, sensor_callback SPO2_Callback)
{
	uint8_t result = VITAL_FAIL;

	hlpuart1.gState = 0;
	MX_LPUART1_UART_Init();

	result = MAX_SPO2_INIT(); //MAX SPO2 Initialization
	if(result != VITAL_SUCCESS)
	{
		goto ERROR;
	}

	result = MAX_SPO2_Processing(spo2, SPO2_Callback); //MAX SPO2 Start Measurement
	if(result != VITAL_SUCCESS)
	{
		goto ERROR;
	}

	return VITAL_SUCCESS;

	ERROR:
	return VITAL_FAIL;
}

uint8_t MAX_SPO2_INIT(void)
{
	uint8_t result;

	result = algohub_init();
	if(result != VITAL_SUCCESS)
	{
		goto ERROR;
	}

	result = LIS2DS12_init();
	if(result != VITAL_SUCCESS)
	{
		goto ERROR;
	}

	return VITAL_SUCCESS;

	ERROR:
	return VITAL_FAIL;
}

uint8_t MAX_SPO2_Processing(SENSOR_DATA_STRUCT *spo2, sensor_callback SPO2_Callback)
{
	uint8_t result;

	result = MAX86176_SPO2_Init();
	if(result != VITAL_SUCCESS)
	{
		goto ERROR;
	}


	//START STATE of SPO2 state machine completed
	SPO2_Callback(STT_STATE);

	result = MAX_WaitForSPO2Samples(spo2, SPO2_Callback);
	if(result != VITAL_SUCCESS)
	{
		goto ERROR;
	}


	return VITAL_SUCCESS;

	ERROR:
	return VITAL_FAIL;
}

uint8_t MAX_SPO2_Start(void)
{
	uint8_t result;

	result = algohub_enable();
	if(result != VITAL_SUCCESS)
	{
		goto ERROR;
	}

	return VITAL_SUCCESS;

	ERROR:
	return VITAL_FAIL;
}


uint8_t MAX_WaitForSPO2Samples(SENSOR_DATA_STRUCT *spo2, sensor_callback SPO2_Callback)
{
	uint32_t indicate = 1;
	spo2->vitals.SPO2.spo2conf = 0;
	memset(adcCountArr,0,sizeof(adcCountArr));
	memset(&algohub_out, 0, sizeof(algohub_out));
	do
	{
		uint32_t pulNotificationValue = 0;
		BaseType_t xReturn;

		///Waiting for data samples///
		xReturn = xTaskNotifyWait(0x0,0xffffffff, &pulNotificationValue, SPO2_Reading_Timeout);
		if(xReturn == FALSE)
		{
			result = ERR_INTERRUPT_TIMEOUT;
			return 0;
		}

		result = MAX_Data_Extraxting();
		if((result != VITAL_SUCCESS) && (result != MAX_FIFO_NOT_FULL))
		{
			goto ERROR;
		}

#if SPO2_SWV
		for(uint8_t i=0; i<Spo2_SWV_Threshold; i++)
		{
			Red    = adcCountArr[IX_PPG][2 + (i*6)];
			Ir     = adcCountArr[IX_PPG][4 + (i*6)];
		}
#endif

		PPG_Frame_Cnt = 0;

		result = MAX_SPO2_Execute_Algorithm(spo2);
		if(result != VITAL_SUCCESS)
		{
			goto ERROR;
		}


		if(spo2->vitals.SPO2.spo2conf >= 0)
		{
			if(spo2->vitals.SPO2.spo2conf == 100)
				updateSpo2Rate(spo2->vitals.SPO2.spo2value);

			if(indicate)
			{
				indicate = 0;
				//DATA COLLECTION STATE of HRM state machine completed
				SPO2_Callback(DC_STATE);
			}
		}

	}while(1);

	//Data processing state of HR state machine completed
	SPO2_Callback(DP_STATE);


	return VITAL_SUCCESS;

	ERROR:
	return VITAL_FAIL;
}

uint8_t MAX_SPO2_Execute_Algorithm(SENSOR_DATA_STRUCT *vital)
{
	uint8_t ret = HAL_ERROR;
	/* Wait for PPG Frame (PPG Frame Rate: 25Hz) */
	lis2ds12_status_reg_get(&acclstatus);
	if(acclstatus.drdy == 1)
	{
		lis2ds12_acceleration_raw_get(accln);
	}

	//***Time critical function. Adding more functions inside may cause buffer overflow at AFE end***/
	if(ppgsampleflag)
	{
		for(uint8_t i=0; i<6; i++)
		{
			algohub_in.ppg_data_in.green1 = adcCountArr[IX_PPG][0 + (i*6)]; 		//PPG_DATA;
			algohub_in.ppg_data_in.green2 = adcCountArr[IX_PPG][1 + (i*6)]; 		//PPG_DATA;
			algohub_in.ppg_data_in.red = adcCountArr[IX_PPG][2 + (i*6)]; 			//PPG_DATA;
			algohub_in.ppg_data_in.ir = adcCountArr[IX_PPG][4 + (i*6)]; 			//PPG_DATA;
			algohub_in.acc_data_in.x = lis2ds12_from_fs8g_to_mg(accln[1]);  //ACC_X_DATA;
			algohub_in.acc_data_in.y = lis2ds12_from_fs8g_to_mg(accln[0]);  //ACC_Y_DATA;
			algohub_in.acc_data_in.z = lis2ds12_from_fs8g_to_mg(accln[2]);  //ACC_Z_DATA;
			ret = algohub_feed_data(&algohub_in);
			printf("Green1 : %ld Green2 : %ld Red : %ld IR : %ld\n\r",algohub_in.ppg_data_in.green1, algohub_in.ppg_data_in.green2, algohub_in.ppg_data_in.red,algohub_in.ppg_data_in.ir);
			if(0 == ret)
			{
				ret = algohub_read_outputfifo(&algohub_out, 1);
				vital->vitals.SPO2.spo2value = (uint8_t)(algohub_out.spo2/10);
				vital->vitals.SPO2.spo2conf = algohub_out.spo2Confidence;
			}
		}
		printf("SPO2: %d,Confidence: %d, quality : %d, r: %d\n\r", algohub_out.spo2, algohub_out.spo2Confidence ,algohub_out.spo2LowSignalQualityFlag,algohub_out.r);
		ppgsampleflag = 0;
	}

	if(0 == ret)
	{
		/* Read if there is AFE request */
		if(algohub_out.algo.isAfeRequestExist)
		{
			/* Apply AFE Requests */
			/* Notify algohub that AFE requests has been applied */
			SPO2_AFE_Count++;
			ret = ah_get_cfg_wearablesuite_aferequest(afereqs);
			pr_info("ah_get_cfg_wearablesuite_aferequest ret %d \n", ret);

			/* LED Current*/
			LED_Current = (((afereqs[0] << 8) & 0xFF00) | afereqs[1]);
			if((LED_Current >> 15) & 0x1)
			{
				LED_Current = ((LED_Current & 0x7FF) * 0.1);
				LED_Current = LED_Current /0.125;
				MAX86176_LED_DriveCurrentPA(0x01,0x00,LED_Current);// MEAS1_DRVA_PA;
				MAX86176_LED_DriveCurrentPA(0x02,0x01,LED_Current); // MEAS2_DRVB_PA; set current to 10mA
				MAX86176_LED_DriveCurrentPA(0x03,0x00,LED_Current);// MEAS3_DRVA_PA; set current to 10mA
			}
			ret = ah_set_cfg_wearablesuite_clear_aferequest(1);
			pr_info("ah_set_cfg_wearablesuite_clear_aferequest ret %d \n", ret);
		}
	}

	return VITAL_SUCCESS;
}

uint8_t MAX86176_SPO2_Init(void)
{
	uint8_t ret;
	ret = MAX86176_Reset(1);								// RESET
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_ReadStatusRegisters();					// read and clear all status registers
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetFIFOThreshold(Spo2_Threshold);
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_PPGTimingData(1); 						// PPG_TIMING_DATA; note that the initial PPG frames may not have an associated ECG sample if they come before the ECG samples have started
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_INT_FCFG(1,0x01);						// enable interrupt 2 - falling edge
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_INT_OCFG(1,0x02);						// int 2 is high
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_INT_EN1(1,0x80);                       //INT 2 EN
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetNdiv(0x13F);						//NDIV=0x13F
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_FR_Clk_Sel(1);							//Fr_Clk -32.768Khz
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	gUseEcg=false;
	gUseEcgPpgTime=true;
	//	ret = MAX86176_SetFIFOThreshold(Spo2_Threshold);			//Extra Buffer - 30
	//	if(ret != HAL_OK)
	//	{
	//		goto ERROR;
	//	}
	//	ret = MAX86176_FR_Clk_Div(0x51E);         		//PPG sampling 25Hz
	//	if(ret != HAL_OK)
	//	{
	//		goto ERROR;
	//	}

	//Measurment1
	ret = MAX86176_MeasurementEnable(0x07);
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_FR_Clk_Div(0x51E);         		//PPG sampling 25Hz
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetLEDDRiver(0x01,0x00,0x01);  	//green led with LEDA
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_setLEDRange(0x01, 0x00);           //led range set to 32mA
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetAdcRangePPG(0x01, 0x00, 0x02);	//measurement 1 16uA ADCgainrange
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetAdcRangePPG(0x01, 0x01, 0x02);	//measurement 1 16uA ADCgainrange
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_LED_DriveCurrentPA(0x01,0x00,0x50);// MEAS1_DRVA_PA; set current to 10mA
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_PPGSelectPD(0x01,0x00,0x00);		//PD1 - Measurement 1 channel 1
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_PPGSelectPD(0x01,0x01,0x02);		//PD3 - Measurement 1 channel 2
	if(ret != HAL_OK)
	{
		goto ERROR;
	}

	//Measurment2 - Red
	ret = MAX86176_SetLEDDRiver(0x02, 0x01, 0x02);    // red led with LEDB
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_setLEDRange(0x02, 0x00);			//led range set to 32mA
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetAverageValue(0x02, 0x02); 		//average 4 samples
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetAdcRangePPG(0x02, 0x00, 0x02);	//measurement 2 16uA ADCgainrange
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetAdcRangePPG(0x02, 0x01, 0x02);	//measurement 2 16uA ADCgainrange
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_LED_DriveCurrentPA(0x02,0x01,ReadRedLedCurrent()); // MEAS2_DRVB_PA; set current to 10mA
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_PPGSelectPD(0x02,0x00,0x01);		//PD2 - Measurement 2 channel 1
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	//Measurment3 - IR
	ret = MAX86176_SetLEDDRiver(0x03, 0x00, 0x00);	//IR with LEDA
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_setLEDRange(0x03, 0x00);			//led range set to 32mA
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetAverageValue(0x03, 0x02); 		//average 4 samples
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetAdcRangePPG(0x03, 0x00, 0x02);	//measurement 3 16uA ADCgainrange
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_SetAdcRangePPG(0x03, 0x01, 0x02);	//measurement 3 16uA ADCgainrange
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_LED_DriveCurrentPA(0x03,0x00,ReadIRLedCurrent());// MEAS3_DRVA_PA; set current to 10mA
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	ret = MAX86176_PPGSelectPD(0x03,0x00,0x01);		//PD2 - Measurement 3 channel 1
	if(ret != HAL_OK)
	{
		goto ERROR;
	}
	return VITAL_SUCCESS;

	ERROR:
	return VITAL_FAIL;
}
