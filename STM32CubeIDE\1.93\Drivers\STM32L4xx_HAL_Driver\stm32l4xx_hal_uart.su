stm32l4xx_hal_uart.c:327:19:HAL_UART_Init	16	static
stm32l4xx_hal_uart.c:400:19:HAL_HalfDuplex_Init	16	static
stm32l4xx_hal_uart.c:473:19:H<PERSON>_LIN_Init	16	static
stm32l4xx_hal_uart.c:570:19:HAL_MultiProcessor_Init	24	static
stm32l4xx_hal_uart.c:644:19:HAL_UART_DeInit	16	static
stm32l4xx_hal_uart.c:690:13:HAL_UART_MspInit	16	static
stm32l4xx_hal_uart.c:705:13:HAL_UART_MspDeInit	16	static
stm32l4xx_hal_uart.c:1149:19:HAL_UART_Transmit	48	static
stm32l4xx_hal_uart.c:1238:19:HAL_UART_Receive	48	static
stm32l4xx_hal_uart.c:1324:19:HAL_UART_Transmit_IT	72	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1415:19:HAL_UART_Receive_IT	48	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1458:19:HAL_UART_Transmit_DMA	48	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1534:19:HAL_UART_Receive_DMA	48	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1572:19:HAL_UART_DMAPause	120	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1606:19:HAL_UART_DMAResume	112	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1641:19:HAL_UART_DMAStop	72	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1716:19:HAL_UART_Abort	136	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1827:19:HAL_UART_AbortTransmit	88	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1893:19:HAL_UART_AbortReceive	112	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:1967:19:HAL_UART_Abort_IT	144	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:2127:19:HAL_UART_AbortTransmit_IT	88	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:2224:19:HAL_UART_AbortReceive_IT	112	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:2322:6:HAL_UART_IRQHandler	240	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:2687:13:HAL_UART_TxCpltCallback	16	static
stm32l4xx_hal_uart.c:2702:13:HAL_UART_TxHalfCpltCallback	16	static
stm32l4xx_hal_uart.c:2717:13:HAL_UART_RxCpltCallback	16	static
stm32l4xx_hal_uart.c:2732:13:HAL_UART_RxHalfCpltCallback	16	static
stm32l4xx_hal_uart.c:2747:13:HAL_UART_ErrorCallback	16	static
stm32l4xx_hal_uart.c:2762:13:HAL_UART_AbortCpltCallback	16	static
stm32l4xx_hal_uart.c:2777:13:HAL_UART_AbortTransmitCpltCallback	16	static
stm32l4xx_hal_uart.c:2792:13:HAL_UART_AbortReceiveCpltCallback	16	static
stm32l4xx_hal_uart.c:2809:13:HAL_UARTEx_RxEventCallback	16	static
stm32l4xx_hal_uart.c:2857:6:HAL_UART_ReceiverTimeout_Config	16	static
stm32l4xx_hal_uart.c:2872:19:HAL_UART_EnableReceiverTimeout	16	static
stm32l4xx_hal_uart.c:2910:19:HAL_UART_DisableReceiverTimeout	16	static
stm32l4xx_hal_uart.c:2948:19:HAL_MultiProcessor_EnableMuteMode	40	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:2968:19:HAL_MultiProcessor_DisableMuteMode	40	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:2988:6:HAL_MultiProcessor_EnterMuteMode	16	static
stm32l4xx_hal_uart.c:2998:19:HAL_HalfDuplex_EnableTransmitter	64	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:3021:19:HAL_HalfDuplex_EnableReceiver	64	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:3045:19:HAL_LIN_SendBreak	16	static
stm32l4xx_hal_uart.c:3090:23:HAL_UART_GetState	24	static
stm32l4xx_hal_uart.c:3106:10:HAL_UART_GetError	16	static
stm32l4xx_hal_uart.c:3154:19:UART_SetConfig	48	static
stm32l4xx_hal_uart.c:3409:6:UART_AdvFeatureConfig	16	static
stm32l4xx_hal_uart.c:3483:19:UART_CheckIdleState	32	static
stm32l4xx_hal_uart.c:3535:19:UART_WaitOnFlagUntilTimeout	120	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:3607:19:UART_Start_Receive_IT	144	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:3707:19:UART_Start_Receive_DMA	96	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:3767:13:UART_EndTxTransfer	64	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:3788:13:UART_EndRxTransfer	88	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:3819:13:UART_DMATransmitCplt	72	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:3853:13:UART_DMATxHalfCplt	24	static
stm32l4xx_hal_uart.c:3871:13:UART_DMAReceiveCplt	120	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:3938:13:UART_DMARxHalfCplt	24	static
stm32l4xx_hal_uart.c:3982:13:UART_DMAError	32	static
stm32l4xx_hal_uart.c:4022:13:UART_DMAAbortOnError	24	static
stm32l4xx_hal_uart.c:4045:13:UART_DMATxAbortCallback	24	static
stm32l4xx_hal_uart.c:4102:13:UART_DMARxAbortCallback	24	static
stm32l4xx_hal_uart.c:4154:13:UART_DMATxOnlyAbortCallback	24	static
stm32l4xx_hal_uart.c:4189:13:UART_DMARxOnlyAbortCallback	24	static
stm32l4xx_hal_uart.c:4222:13:UART_TxISR_8BIT	64	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:4255:13:UART_TxISR_16BIT	72	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:4292:13:UART_TxISR_8BIT_FIFOEN	72	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:4332:13:UART_TxISR_16BIT_FIFOEN	72	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:4374:13:UART_EndTransmit_IT	40	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:4399:13:UART_RxISR_8BIT	96	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:4480:13:UART_RxISR_16BIT	96	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:4564:13:UART_RxISR_8BIT_FIFOEN	160	static,ignoring_inline_asm
stm32l4xx_hal_uart.c:4712:13:UART_RxISR_16BIT_FIFOEN	168	static,ignoring_inline_asm
