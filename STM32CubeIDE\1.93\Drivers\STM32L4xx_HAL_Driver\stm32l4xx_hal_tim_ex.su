stm32l4xx_hal_tim_ex.c:137:19:H<PERSON>_TIMEx_HallSensor_Init	48	static
stm32l4xx_hal_tim_ex.c:238:19:HAL_TIMEx_HallSensor_DeInit	16	static
stm32l4xx_hal_tim_ex.c:283:13:HAL_TIMEx_HallSensor_MspInit	16	static
stm32l4xx_hal_tim_ex.c:298:13:HAL_TIMEx_HallSensor_MspDeInit	16	static
stm32l4xx_hal_tim_ex.c:313:19:HAL_TIMEx_HallSensor_Start	24	static
stm32l4xx_hal_tim_ex.c:366:19:HAL_TIMEx_HallSensor_Stop	16	static
stm32l4xx_hal_tim_ex.c:393:19:HAL_TIMEx_HallSensor_Start_IT	24	static
stm32l4xx_hal_tim_ex.c:449:19:HAL_TIMEx_HallSensor_Stop_IT	16	static
stm32l4xx_hal_tim_ex.c:481:19:HAL_TIMEx_HallSensor_Start_DMA	32	static
stm32l4xx_hal_tim_ex.c:555:19:HAL_TIMEx_HallSensor_Stop_DMA	16	static
stm32l4xx_hal_tim_ex.c:616:19:HAL_TIMEx_OCN_Start	24	static
stm32l4xx_hal_tim_ex.c:667:19:HAL_TIMEx_OCN_Stop	16	static
stm32l4xx_hal_tim_ex.c:699:19:HAL_TIMEx_OCN_Start_IT	24	static
stm32l4xx_hal_tim_ex.c:781:19:HAL_TIMEx_OCN_Stop_IT	24	static
stm32l4xx_hal_tim_ex.c:850:19:HAL_TIMEx_OCN_Start_DMA	32	static
stm32l4xx_hal_tim_ex.c:976:19:HAL_TIMEx_OCN_Stop_DMA	16	static
stm32l4xx_hal_tim_ex.c:1071:19:HAL_TIMEx_PWMN_Start	24	static
stm32l4xx_hal_tim_ex.c:1121:19:HAL_TIMEx_PWMN_Stop	16	static
stm32l4xx_hal_tim_ex.c:1153:19:HAL_TIMEx_PWMN_Start_IT	24	static
stm32l4xx_hal_tim_ex.c:1234:19:HAL_TIMEx_PWMN_Stop_IT	24	static
stm32l4xx_hal_tim_ex.c:1304:19:HAL_TIMEx_PWMN_Start_DMA	32	static
stm32l4xx_hal_tim_ex.c:1430:19:HAL_TIMEx_PWMN_Stop_DMA	16	static
stm32l4xx_hal_tim_ex.c:1513:19:HAL_TIMEx_OnePulseN_Start	24	static
stm32l4xx_hal_tim_ex.c:1554:19:HAL_TIMEx_OnePulseN_Stop	24	static
stm32l4xx_hal_tim_ex.c:1589:19:HAL_TIMEx_OnePulseN_Start_IT	24	static
stm32l4xx_hal_tim_ex.c:1636:19:HAL_TIMEx_OnePulseN_Stop_IT	24	static
stm32l4xx_hal_tim_ex.c:1714:19:HAL_TIMEx_ConfigCommutEvent	24	static
stm32l4xx_hal_tim_ex.c:1770:19:HAL_TIMEx_ConfigCommutEvent_IT	24	static
stm32l4xx_hal_tim_ex.c:1827:19:HAL_TIMEx_ConfigCommutEvent_DMA	24	static
stm32l4xx_hal_tim_ex.c:1876:19:HAL_TIMEx_MasterConfigSynchronization	24	static
stm32l4xx_hal_tim_ex.c:1949:19:HAL_TIMEx_ConfigBreakDeadTime	24	static
stm32l4xx_hal_tim_ex.c:2013:19:HAL_TIMEx_ConfigBreakInput	48	static
stm32l4xx_hal_tim_ex.c:2365:19:HAL_TIMEx_RemapConfig	24	static
stm32l4xx_hal_tim_ex.c:2410:19:HAL_TIMEx_GroupChannel5	16	static
stm32l4xx_hal_tim_ex.c:2460:13:HAL_TIMEx_CommutCallback	16	static
stm32l4xx_hal_tim_ex.c:2474:13:HAL_TIMEx_CommutHalfCpltCallback	16	static
stm32l4xx_hal_tim_ex.c:2489:13:HAL_TIMEx_BreakCallback	16	static
stm32l4xx_hal_tim_ex.c:2504:13:HAL_TIMEx_Break2Callback	16	static
stm32l4xx_hal_tim_ex.c:2537:22:HAL_TIMEx_HallSensor_GetState	16	static
stm32l4xx_hal_tim_ex.c:2552:29:HAL_TIMEx_GetChannelNState	24	static
stm32l4xx_hal_tim_ex.c:2581:6:TIMEx_DMACommutationCplt	24	static
stm32l4xx_hal_tim_ex.c:2600:6:TIMEx_DMACommutationHalfCplt	24	static
stm32l4xx_hal_tim_ex.c:2620:13:TIM_DMADelayPulseNCplt	24	static
stm32l4xx_hal_tim_ex.c:2679:6:TIM_DMAErrorCCxN	24	static
stm32l4xx_hal_tim_ex.c:2724:13:TIM_CCxNChannelCmd	32	static
