Drawable.hpp:62:13:touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:62:13:virtual touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:132:23:virtual touchgfx::Drawable* touchgfx::Drawable::getFirstChild()	16	static
Drawable.hpp:269:18:virtual void touchgfx::Drawable::setX(int16_t)	16	static
Drawable.hpp:281:18:virtual void touchgfx::Drawable::setY(int16_t)	16	static
Drawable.hpp:310:18:virtual void touchgfx::Drawable::setWidth(int16_t)	16	static
Drawable.hpp:322:18:virtual void touchgfx::Drawable::setHeight(int16_t)	16	static
Drawable.hpp:332:18:virtual void touchgfx::Drawable::childGeometryChanged()	16	static
Drawable.hpp:343:18:virtual void touchgfx::Drawable::handleClickEvent(const touchgfx::ClickEvent&)	16	static
Drawable.hpp:354:18:virtual void touchgfx::Drawable::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
Drawable.hpp:442:18:virtual void touchgfx::Drawable::handleDragEvent(const touchgfx::DragEvent&)	16	static
Drawable.hpp:452:18:virtual void touchgfx::Drawable::handleTickEvent()	16	static
Drawable.hpp:488:10:bool touchgfx::Drawable::isVisible() const	16	static
Drawable.hpp:500:10:bool touchgfx::Drawable::isTouchable() const	16	static
Drawable.hpp:539:18:virtual void touchgfx::Drawable::moveTo(int16_t, int16_t)	16	static
Drawable.hpp:576:10:void touchgfx::Drawable::resetDrawChainCache()	16	static
Drawable.hpp:654:18:virtual void touchgfx::Drawable::setupDrawChain(const touchgfx::Rect&, touchgfx::Drawable**)	24	static
Screen.hpp:47:13:touchgfx::Screen::~Screen()	16	static
Screen.hpp:47:13:virtual touchgfx::Screen::~Screen()	16	static
Screen.hpp:94:18:virtual void touchgfx::Screen::setupScreen()	16	static
Screen.hpp:105:18:virtual void touchgfx::Screen::afterTransition()	16	static
Screen.hpp:116:18:virtual void touchgfx::Screen::tearDownScreen()	16	static
Screen.hpp:144:18:virtual void touchgfx::Screen::handleTickEvent()	16	static
Screen.hpp:154:18:virtual void touchgfx::Screen::handleKeyEvent(uint8_t)	16	static
Widget.hpp:47:18:virtual void touchgfx::Widget::getLastChild(int16_t, int16_t, touchgfx::Drawable**)	24	static
Widget.hpp:36:7:touchgfx::Widget::~Widget()	16	static
Widget.hpp:36:7:virtual touchgfx::Widget::~Widget()	16	static
AbstractButton.hpp:32:7:touchgfx::AbstractButton::~AbstractButton()	16	static
AbstractButton.hpp:32:7:virtual touchgfx::AbstractButton::~AbstractButton()	16	static
TypedText.hpp:65:13:touchgfx::TypedText::~TypedText()	16	static
TypedText.hpp:65:13:virtual touchgfx::TypedText::~TypedText()	16	static
TextArea.hpp:39:7:touchgfx::TextArea::~TextArea()	16	static
TextArea.hpp:39:7:virtual touchgfx::TextArea::~TextArea()	16	static
Container.hpp:40:7:touchgfx::Container::~Container()	16	static
Container.hpp:40:7:virtual touchgfx::Container::~Container()	16	static
DeveloperModeBase.hpp:22:13:DeveloperModeBase::~DeveloperModeBase()	16	static
DeveloperModeBase.hpp:22:13:virtual DeveloperModeBase::~DeveloperModeBase()	16	static
DeveloperMode.hpp:10:13:DeveloperMode::~DeveloperMode()	16	static
DeveloperMode.hpp:10:13:virtual DeveloperMode::~DeveloperMode()	16	static
View.hpp:36:7:touchgfx::View<DEVELOPER_MODEPresenter>::~View()	16	static
View.hpp:36:7:virtual touchgfx::View<DEVELOPER_MODEPresenter>::~View()	16	static
DEVELOPER_MODEViewBase.hpp:20:13:DEVELOPER_MODEViewBase::~DEVELOPER_MODEViewBase()	16	static
DEVELOPER_MODEViewBase.hpp:20:13:virtual DEVELOPER_MODEViewBase::~DEVELOPER_MODEViewBase()	16	static
DEVELOPER_MODEView.hpp:11:13:DEVELOPER_MODEView::~DEVELOPER_MODEView()	16	static
DEVELOPER_MODEView.hpp:11:13:virtual DEVELOPER_MODEView::~DEVELOPER_MODEView()	16	static
DEVELOPER_MODEView.cpp:2:1:DEVELOPER_MODEView::DEVELOPER_MODEView()	16	static
DEVELOPER_MODEView.cpp:7:6:virtual void DEVELOPER_MODEView::setupScreen()	16	static
DEVELOPER_MODEView.cpp:12:6:virtual void DEVELOPER_MODEView::tearDownScreen()	16	static
Button.hpp:31:7:touchgfx::Button::~Button()	16	static
Button.hpp:31:7:virtual touchgfx::Button::~Button()	16	static
TextAreaWithWildcard.hpp:34:7:touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
TextAreaWithWildcard.hpp:34:7:virtual touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
Box.hpp:29:7:touchgfx::Box::~Box()	16	static
Box.hpp:29:7:virtual touchgfx::Box::~Box()	16	static
ButtonWithLabel.hpp:38:7:touchgfx::ButtonWithLabel::~ButtonWithLabel()	16	static
ButtonWithLabel.hpp:38:7:virtual touchgfx::ButtonWithLabel::~ButtonWithLabel()	16	static
ToggleButton.hpp:32:7:touchgfx::ToggleButton::~ToggleButton()	16	static
ToggleButton.hpp:32:7:virtual touchgfx::ToggleButton::~ToggleButton()	16	static
Callback.hpp:357:8:touchgfx::Callback<DeveloperModeBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<DeveloperModeBase, const touchgfx::AbstractButton&>::~Callback()	16	static
ScrollableContainer.hpp:46:7:touchgfx::ScrollableContainer::~ScrollableContainer()	16	static
ScrollableContainer.hpp:46:7:virtual touchgfx::ScrollableContainer::~ScrollableContainer()	16	static
Callback.hpp:357:8:touchgfx::Callback<DEVELOPER_MODEViewBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<DEVELOPER_MODEViewBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = DeveloperModeBase; T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = DeveloperModeBase; T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = DEVELOPER_MODEViewBase; T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = DEVELOPER_MODEViewBase; T1 = const touchgfx::AbstractButton&]	16	static
