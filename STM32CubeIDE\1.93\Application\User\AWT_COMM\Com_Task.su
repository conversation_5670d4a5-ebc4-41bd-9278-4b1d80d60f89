Com_Task.c:36:9:isCommConnected	4	static
Com_Task.c:48:9:isCommDisconnected	4	static
Com_Task.c:61:9:setCommState	16	static
Com_Task.c:67:6:Comm_Mngr_Task	40	static
Com_Task.c:108:10:startITimer	32	static
Com_Task.c:119:10:stopITimer	16	static
Com_Task.c:131:10:startCRTimer	32	static
Com_Task.c:142:10:stopCRTimer	16	static
Com_Task.c:154:10:IsComMessageEmpty	8	static
Com_Task.c:159:6:triggerFromPrimitive	32	static
Com_Task.c:172:9:triggerCommMngr	16	static
Com_Task.c:194:6:crTimeoutCallback	32	static
Com_Task.c:206:6:iTimeoutCallback	32	static
Com_Task.c:232:6:Create_ComTask	32	static
Com_Task.c:253:6:Comm_Can_Sleep	4	static
Com_Task.c:258:6:Comm_Sleep_Clear_isr	8	static
Com_Task.c:268:6:Comm_Sleep_Set	8	static
Com_Task.c:277:6:Comm_Sleep_Clear	4	static
Com_Task.c:279:6:Handle_Sleep	4	static
Com_Task.c:285:6:preparetest	8	static
