stm32l4xx_ll_adc.h:2443:22:LL_<PERSON><PERSON>_SetCommonClock	16	static
stm32l4xx_ll_adc.h:2507:22:LL_ADC_SetCommonPathInternalCh	16	static
stm32l4xx_ll_adc.h:2588:26:LL_<PERSON><PERSON>_GetCommonPathInternalCh	16	static
stm32l4xx_ll_adc.h:2927:22:LL_ADC_SetOffset	32	static
stm32l4xx_ll_adc.h:3006:26:LL_ADC_GetOffsetChannel	24	static
stm32l4xx_ll_adc.h:3065:22:LL_ADC_SetOffsetState	32	static
stm32l4xx_ll_adc.h:3113:22:LL_ADC_SetSamplingTimeCommonConfig	16	static
stm32l4xx_ll_adc.h:3246:26:LL_ADC_REG_IsTriggerSourceSWStart	16	static
stm32l4xx_ll_adc.h:3548:22:LL_ADC_REG_SetSequencerRanks	32	static
stm32l4xx_ll_adc.h:3972:26:LL_ADC_INJ_IsTriggerSourceSWStart	16	static
stm32l4xx_ll_adc.h:4714:22:LL_ADC_SetChannelSamplingTime	32	static
stm32l4xx_ll_adc.h:4868:22:LL_ADC_SetChannelSingleDiff	24	static
stm32l4xx_ll_adc.h:5075:22:LL_ADC_SetAnalogWDMonitChannels	32	static
stm32l4xx_ll_adc.h:5319:22:LL_ADC_ConfigAnalogWDThresholds	32	static
stm32l4xx_ll_adc.h:5923:22:LL_ADC_DisableDeepPowerDown	16	static
stm32l4xx_ll_adc.h:5937:26:LL_ADC_IsDeepPowerDownEnabled	16	static
stm32l4xx_ll_adc.h:5956:22:LL_ADC_EnableInternalRegulator	16	static
stm32l4xx_ll_adc.h:5986:26:LL_ADC_IsInternalRegulatorEnabled	16	static
stm32l4xx_ll_adc.h:6007:22:LL_ADC_Enable	16	static
stm32l4xx_ll_adc.h:6027:22:LL_ADC_Disable	16	static
stm32l4xx_ll_adc.h:6046:26:LL_ADC_IsEnabled	16	static
stm32l4xx_ll_adc.h:6057:26:LL_ADC_IsDisableOngoing	16	static
stm32l4xx_ll_adc.h:6132:22:LL_ADC_REG_StartConversion	16	static
stm32l4xx_ll_adc.h:6152:22:LL_ADC_REG_StopConversion	16	static
stm32l4xx_ll_adc.h:6168:26:LL_ADC_REG_IsConversionOngoing	16	static
stm32l4xx_ll_adc.h:6335:22:LL_ADC_INJ_StopConversion	16	static
stm32l4xx_ll_adc.h:6351:26:LL_ADC_INJ_IsConversionOngoing	16	static
stm32l4xx_ll_adc.h:6730:22:LL_ADC_ClearFlag_AWD1	16	static
stm32l4xx_ll_adc.h:6741:22:LL_ADC_ClearFlag_AWD2	16	static
stm32l4xx_ll_adc.h:6752:22:LL_ADC_ClearFlag_AWD3	16	static
stm32l4xx_ll_adc.h:7125:22:LL_ADC_EnableIT_AWD1	16	static
stm32l4xx_ll_adc.h:7136:22:LL_ADC_EnableIT_AWD2	16	static
stm32l4xx_ll_adc.h:7147:22:LL_ADC_EnableIT_AWD3	16	static
stm32l4xx_ll_adc.h:7246:22:LL_ADC_DisableIT_AWD1	16	static
stm32l4xx_ll_adc.h:7257:22:LL_ADC_DisableIT_AWD2	16	static
stm32l4xx_ll_adc.h:7268:22:LL_ADC_DisableIT_AWD3	16	static
stm32l4xx_hal_adc.c:401:19:HAL_ADC_Init	40	static
stm32l4xx_hal_adc.c:706:19:HAL_ADC_DeInit	24	static
stm32l4xx_hal_adc.c:908:13:HAL_ADC_MspInit	16	static
stm32l4xx_hal_adc.c:925:13:HAL_ADC_MspDeInit	16	static
stm32l4xx_hal_adc.c:1215:19:HAL_ADC_Start	24	static
stm32l4xx_hal_adc.c:1350:19:HAL_ADC_Stop	24	static
stm32l4xx_hal_adc.c:1404:19:HAL_ADC_PollForConversion	32	static
stm32l4xx_hal_adc.c:1583:19:HAL_ADC_PollForEvent	32	static
stm32l4xx_hal_adc.c:1728:19:HAL_ADC_Start_IT	24	static
stm32l4xx_hal_adc.c:1935:19:HAL_ADC_Stop_IT	24	static
stm32l4xx_hal_adc.c:1988:19:HAL_ADC_Start_DMA	32	static
stm32l4xx_hal_adc.c:2129:19:HAL_ADC_Stop_DMA	24	static
stm32l4xx_hal_adc.c:2214:10:HAL_ADC_GetValue	16	static
stm32l4xx_hal_adc.c:2231:6:HAL_ADC_IRQHandler	40	static
stm32l4xx_hal_adc.c:2601:13:HAL_ADC_ConvCpltCallback	16	static
stm32l4xx_hal_adc.c:2616:13:HAL_ADC_ConvHalfCpltCallback	16	static
stm32l4xx_hal_adc.c:2631:13:HAL_ADC_LevelOutOfWindowCallback	16	static
stm32l4xx_hal_adc.c:2653:13:HAL_ADC_ErrorCallback	16	static
stm32l4xx_hal_adc.c:2699:19:HAL_ADC_ConfigChannel	224	static,ignoring_inline_asm
stm32l4xx_hal_adc.c:2956:19:HAL_ADC_AnalogWDGConfig	64	static,ignoring_inline_asm
stm32l4xx_hal_adc.c:3210:10:HAL_ADC_GetState	16	static
stm32l4xx_hal_adc.c:3224:10:HAL_ADC_GetError	16	static
stm32l4xx_hal_adc.c:3254:19:ADC_ConversionStop	40	static
stm32l4xx_hal_adc.c:3382:19:ADC_Enable	24	static
stm32l4xx_hal_adc.c:3450:19:ADC_Disable	24	static
stm32l4xx_hal_adc.c:3508:6:ADC_DMAConvCplt	24	static
stm32l4xx_hal_adc.c:3586:6:ADC_DMAHalfConvCplt	24	static
stm32l4xx_hal_adc.c:3604:6:ADC_DMAError	24	static
