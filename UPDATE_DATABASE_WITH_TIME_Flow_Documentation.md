# UPDATE_DATABASE_WITH_TIME Flow Documentation

## Overview

The `UPDATE_DATABASE_WITH_TIME` function is a critical component in the AWT wearable device that manages the central database storage of all vital sign measurements. It serves as the primary interface for storing sensor data with timestamps into the global database structure.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    Sensor Data Sources                          │
│  • MAX SPO2 Algorithm  • Heart Rate Monitor  • ECG Sensor      │
│  • Blood Pressure      • Temperature Sensor  • Pedometer       │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                SENSOR_DATA_STRUCT                               │
│  • REQUEST_ID (GUI, SCH, COMM, SOS)                           │
│  • VITAL_REQUESTED_PARAM (SpO2, Heart_Rate, etc.)             │
│  • vitals (Union of all vital data structures)                │
│  • UNIX_TIME (Timestamp)                                      │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│            UPDATE_DATABASE_WITH_TIME()                         │
│  • Parameter Validation  • Data Type Switching                │
│  • Timestamp Assignment  • History Management                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 GLOBAL_DATA                                    │
│  • Persistent Storage in .usr_config_area                     │
│  • Individual Vital Structures  • Historical Data             │
│  • Raw Signal Pointers  • Timestamp Records                  │
└─────────────────────────────────────────────────────────────────┘
```

## Function Implementation

### Function Signature:
```c
void UPDATE_DATABASE_WITH_TIME(SENSOR_DATA_STRUCT *VITAL_DATA)
```

### Core Functionality:
The function performs a switch-case operation based on the vital parameter type and updates the corresponding global database structure with:
1. **Measured Values**: Actual sensor readings
2. **Timestamps**: Unix timestamp for when measurement was taken
3. **Historical Data**: Maintains rolling history arrays
4. **Raw Data Pointers**: Links to raw sensor data (when available)

## Data Flow Analysis

### Input Structure:
```c
typedef struct {
    REQUESTED_ID REQUEST_ID;              // Who requested the measurement
    VITAL_PARAMETER_ID VITAL_REQUESTED_PARAM; // Type of vital sign
    data vitals;                          // Union containing measurement data
    uint32_t UNIX_TIME;                   // Measurement timestamp
    uint8_t listener;                     // Response destination
} SENSOR_DATA_STRUCT;
```

### Global Database Structure:
```c
typedef struct {
    HR_STRUCT       HEART_RATE;          // Heart rate data
    SPO2_STRUCT     SPO2;                // SPO2 data
    BP_STRUCT       BP;                  // Blood pressure data
    ECG_STRUCT      ECG;                 // ECG data
    TEMPERATURE_STRUCT TEMPERATURE;       // Temperature data
    GSR_STRUCT      GSR;                 // GSR data
    BATTERY_STRUCT  BAT;                 // Battery data
    PEDO_STRUCT     PEDOMETER;           // Pedometer data
    RAW_SIGNALS     RAW_SIGNALS_VITALS;  // Raw signal pointers
} SENSOR_VITAL_DATA;
```

## Detailed Processing by Vital Type

### 1. Heart Rate Processing:
```c
case Heart_Rate:
#if DR_DATA_HARDCODED_UI
    GLOBAL_DATA.HEART_RATE.hrmValue = 72;
    GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE = 1640350265;
#else
    GLOBAL_DATA.HEART_RATE.hrmValue = VITAL_DATA->vitals.HRM.hrmValue;
    GLOBAL_DATA.HEART_RATE.TIME_HEART_RATE = VITAL_DATA->UNIX_TIME;
    GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgGreen_HRPtr = VITAL_DATA->vitals.HRM.rawPpgGreenPtr;
    GLOBAL_DATA.HEART_RATE.HRM_HISTORY[HHcount] = GLOBAL_DATA.HEART_RATE.hrmValue;
#endif
```

**Processing Steps:**
1. **Conditional Compilation**: Uses hardcoded values if `DR_DATA_HARDCODED_UI` is enabled
2. **Value Assignment**: Stores heart rate value from sensor data
3. **Timestamp Storage**: Records measurement time
4. **Raw Data Linking**: Links to raw PPG green channel data
5. **History Update**: Adds current value to rolling history array

### 2. SPO2 Processing:
```c
case SpO2:
#if DR_DATA_HARDCODED_UI
    GLOBAL_DATA.SPO2.spo2value = 98;
    GLOBAL_DATA.SPO2.TIME_SPO2 = Get_Current_Unix_Time();
#else
    GLOBAL_DATA.SPO2.spo2value = VITAL_DATA->vitals.SPO2.spo2value;
    GLOBAL_DATA.SPO2.TIME_SPO2 = VITAL_DATA->UNIX_TIME;
    GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgRed_SPO2Ptr = VITAL_DATA->vitals.SPO2.rawPpgRedPtr;
    GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgIr_SPO2Ptr = VITAL_DATA->vitals.SPO2.rawPpgIrPtr;
    GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgAmb_SPO2Ptr = VITAL_DATA->vitals.SPO2.rawPpgAmbPtr;
#endif
    GLOBAL_DATA.SPO2.SPO2_HISTORY[HHcount] = GLOBAL_DATA.SPO2.spo2value;
```

**Processing Steps:**
1. **SPO2 Value Storage**: Main SPO2 percentage value
2. **Timestamp Recording**: When measurement was taken
3. **Multi-channel Raw Data**: Links to Red, IR, and Ambient PPG channels
4. **History Management**: Updates SPO2 history array

### 3. Blood Pressure Processing:
```c
case BP:
    GLOBAL_DATA.BP.systolic = VITAL_DATA->vitals.BP.systolic;
    GLOBAL_DATA.BP.diastolic = VITAL_DATA->vitals.BP.diastolic;
    GLOBAL_DATA.BP.PQRST_parameters.ECG_hr = VITAL_DATA->vitals.BP.PQRST_parameters.ECG_hr;
    GLOBAL_DATA.BP.TIME_BLOOD_PRESSURE = VITAL_DATA->UNIX_TIME;
    GLOBAL_DATA.RAW_SIGNALS_VITALS.rawPpgGreen_BpPtr = VITAL_DATA->vitals.BP.rawppgGreenPtr;
    GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_BpPtr = VITAL_DATA->vitals.BP.rawEcgPtr;
```

**Processing Steps:**
1. **Dual Value Storage**: Systolic and diastolic pressure values
2. **ECG Parameters**: Heart rate derived from ECG analysis
3. **Timestamp Recording**: Blood pressure measurement time
4. **Raw Signal Links**: PPG and ECG raw data pointers
5. **History Management**: Rolling history with timestamps

### 4. ECG Processing:
```c
case ECG:
    GLOBAL_DATA.ECG.PQRST_parameters.ECG_hr = VITAL_DATA->vitals.ECG.PQRST_parameters.ECG_hr;
    GLOBAL_DATA.ECG.PQRST_parameters.Inferences = VITAL_DATA->vitals.ECG.PQRST_parameters.Inferences;
    GLOBAL_DATA.ECG.PQRST_parameters.pr = VITAL_DATA->vitals.ECG.PQRST_parameters.pr;
    GLOBAL_DATA.ECG.PQRST_parameters.qrs = VITAL_DATA->vitals.ECG.PQRST_parameters.qrs;
    GLOBAL_DATA.ECG.PQRST_parameters.qt = VITAL_DATA->vitals.ECG.PQRST_parameters.qt;
    GLOBAL_DATA.ECG.PQRST_parameters.rr = VITAL_DATA->vitals.ECG.PQRST_parameters.rr;
    GLOBAL_DATA.ECG.TIME_ECG = VITAL_DATA->UNIX_TIME;
    GLOBAL_DATA.RAW_SIGNALS_VITALS.rawEcg_ECGPtr = VITAL_DATA->vitals.ECG.rawEcgPtr;
```

**Processing Steps:**
1. **PQRST Parameters**: Complete ECG waveform analysis results
2. **Clinical Inferences**: Arrhythmia detection results (NSR, AFib, etc.)
3. **Timing Intervals**: PR, QRS, QT, RR intervals
4. **Raw ECG Data**: Link to raw ECG signal buffer

## Memory Management

### Persistent Storage:
```c
SENSOR_VITAL_DATA __attribute__((section (".usr_config_area"))) GLOBAL_DATA;
```

**Characteristics:**
- **Location**: Special memory section `.usr_config_area`
- **Persistence**: Survives power cycles and resets
- **Size**: ~2KB total structure size
- **Access**: Global access across all modules

### History Management:
```c
// Rolling history arrays (24 entries each)
uint8_t HRM_HISTORY[24];     // Heart rate history
uint8_t SPO2_HISTORY[24];    // SPO2 history
float CBT_HISTORY[24];       // Temperature history
uint8_t BP_HISTORY[24];      // Blood pressure history
uint32_t TIME_BP[12];        // BP timestamp history
```

**History Features:**
- **Circular Buffers**: Automatic wraparound when full
- **24-Hour Coverage**: One entry per hour for daily trends
- **Timestamp Correlation**: Separate timestamp arrays for time-critical data

## Integration Points

### 1. Sensor Task Integration:
```c
// Called after successful sensor measurement
if (Result == VITAL_SUCCESS) {
    UPDATE_DATABASE_WITH_TIME(&Requested_Parameter);
    Sensor_Callback(END_STATE);
}
```

### 2. Scheduler Task Integration:
```c
// Periodic measurements
Requested_Parameter.VITAL_REQUESTED_PARAM = SpO2;
UPDATE_DATABASE_WITH_TIME(&Requested_Parameter);
```

### 3. SOS Task Integration:
```c
// Emergency monitoring
_vital = Heart_Rate;
HRMParameter(SOS_Callback);
UPDATE_DATABASE_WITH_TIME(&Requested_Parameter);
```

### 4. GUI Integration:
```c
// Display updates
SENSOR_DATA_STRUCT data_pack;
data_pack.REQUEST_ID = GUI;
RETRIEVE_VITAL_PARAMETER(&data_pack);  // Reads from GLOBAL_DATA
```

## Data Retrieval Flow

### Retrieval Function:
```c
void RETRIEVE_VITAL_PARAMETER(SENSOR_DATA_STRUCT *VITAL_DATA)
```

**Process:**
1. **Request Analysis**: Check REQUEST_ID and VITAL_REQUESTED_PARAM
2. **Data Extraction**: Copy data from GLOBAL_DATA to output structure
3. **Error Handling**: Include error codes if measurement failed
4. **Raw Data Links**: Provide pointers to raw signal data
5. **Timestamp Provision**: Include measurement timestamps

## Compilation Flags

### Debug and Testing Flags:
```c
#define DR_DATA_HARDCODED_UI    0    // Use hardcoded values for testing
#define RAW_DATA               1    // Enable raw data storage
#define DR_Mask_Lower_Layer    0    // Bypass actual sensors
```

### Feature Control:
- **`DR_DATA_HARDCODED_UI`**: Forces hardcoded values for UI testing
- **`RAW_DATA`**: Enables raw signal data storage and transmission
- **`DR_Mask_Lower_Layer`**: Simulates sensor data without hardware

## Error Handling

### Error Integration:
```c
int32_t err_no = getError();
VITAL_DATA->vitals.HRM.hrmValue = (err_no == 0) ? GLOBAL_DATA.HEART_RATE.hrmValue : err_no;
```

**Error Types:**
- **Sensor Errors**: Hardware communication failures
- **Algorithm Errors**: Processing failures
- **Timeout Errors**: Measurement timeout conditions
- **Quality Errors**: Poor signal quality indicators

This comprehensive flow ensures reliable, timestamped storage of all vital sign measurements with full traceability and historical tracking.
