################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (9-2020-q2-update)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
D:/spo2_zcross/max_zcrossing/Core/Src/system_stm32l4xx.c 

OBJS += \
./Drivers/CMSIS/system_stm32l4xx.o 

C_DEPS += \
./Drivers/CMSIS/system_stm32l4xx.d 


# Each subdirectory must supply rules for building sources it contributes
Drivers/CMSIS/system_stm32l4xx.o: D:/spo2_zcross/max_zcrossing/Core/Src/system_stm32l4xx.c Drivers/CMSIS/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m4 -std=gnu11 -g3 -DSTM32L4R9xx -DUSE_HAL_DRIVER -DARM_MATH_CM4 -D__FPU_PRESENT=1 -DRTT_PRINT=0 -DHW_1p1=0 -DBOARD_1p75=0 -DDEV_MODE=0 -DRAW_DATA=1 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DBOARD_1V91=0 -DRING_BUFFER=1 -DBOARD_1V92=1 -DDISABLE_WIFI=1 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DTRANSPORT_MANAGER_DEBUG=0 -DCOMM_MANAGER_DEBUG=0 -DST -DWIRELESS_POLL_TIMER=0 -DMAX_SENSOR=1 -c -I../../Core/Boards/Inc/1.93 -I../../Core/Inc -I../../Drivers/CMSIS/Include -I../../TouchGFX/target -I../../TouchGFX/App -I../../Drivers/STM32L4xx_HAL_Driver/Inc -I../../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../../TouchGFX/target/generated -I../../Drivers/BSP/Components/Common -I../../Drivers/STM32L4xx_HAL_Driver/Inc/Legacy -I../../Drivers/BSP/STM32L4R9I-DISCOVERY -I../../Drivers/CMSIS/Device/ST/STM32L4xx/Include -I../../Middlewares/Third_Party/FreeRTOS/Source/include -I../../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F -I../../Drivers/BSP/Components/ft3x67 -I../../Drivers/BSP/Components -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -I../../STM32CubeIDE/Drivers/STM32L4xx_HAL_Driver -I../../STM32CubeIDE/Application/User/AWT_BUS -I../../STM32CubeIDE/Application/User/AWT_LSM6DSOX -I../../STM32CubeIDE/Application/User/AWT_PMIC -I../../STM32CubeIDE/Application/User/AWT_AS7050 -I../../STM32CubeIDE/Application/User/AWT_AS7050/as7050-chip-lib/inc -I../../STM32CubeIDE/Application/User/AWT_AS7050/css-sw-utilities/c/error_codes/inc -I../../STM32CubeIDE/Application/User/AWT_AS7050/css-sw-utilities/c/i2c_drv/lis2dh12/inc -I../../STM32CubeIDE/Application/User/AWT_AS7050/css-sw-utilities/lib/st_driver_lis2dh12/inc -I../../STM32CubeIDE/Application/User/AWT_AS7050/css-sw-utilities/c/std_inc/inc -I../../STM32CubeIDE/Application/User/AWT_AS7050/as7050-app-manager/inc/app_mngr -I../../STM32CubeIDE/Application/User/AWT_AS7050/as7050-chip-lib/inc/osal -I../../STM32CubeIDE/Application/User/AWT_WIFI_BOOT/Inc -I../../STM32CubeIDE/Application/User/AWT_FILTERS -I../../STM32CubeIDE/Application/User/AWT_EXTRA_DEBUG_LIB -I../../STM32CubeIDE/Application/User/AWT_DA14531_BLE -I../../STM32CubeIDE/Application/User -I../../STM32CubeIDE/Application/User/AWT_AS7050/as7050-chip-lib/inc/agc -I../../STM32CubeIDE/Application/User/AWT_AS7050/vital-signs-acc/inc -I../../STM32CubeIDE/Application/User/AWT_AS7050/bio-app-handler/inc -I../../STM32CubeIDE/Application/User/AWT_AS7050/bio-app-handler/inc/hrm_a0 -I../../STM32CubeIDE/Application/User/AWT_AS7050/bio-app-handler/inc/spo2_a0 -I../../STM32CubeIDE/Application/User/Ringbuffer -I../../STM32CubeIDE/Application/User/AWT_SENSORS_DATA -I../../STM32CubeIDE/Application/User/AWT_AS7050/vitalsigns-algorithms/spo2/inc -I../../STM32CubeIDE/Application/User/AWT_SCH -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_COMM" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_DEBUG" -I../../algo_quent/ECG/API -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_TACTILE_NOTIF" -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/AWT_MAX/Inc" -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

