Drawable.hpp:62:13:touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:62:13:virtual touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:132:23:virtual touchgfx::Drawable* touchgfx::Drawable::getFirstChild()	16	static
Drawable.hpp:269:18:virtual void touchgfx::Drawable::setX(int16_t)	16	static
Drawable.hpp:281:18:virtual void touchgfx::Drawable::setY(int16_t)	16	static
Drawable.hpp:310:18:virtual void touchgfx::Drawable::setWidth(int16_t)	16	static
Drawable.hpp:322:18:virtual void touchgfx::Drawable::setHeight(int16_t)	16	static
Drawable.hpp:332:18:virtual void touchgfx::Drawable::childGeometryChanged()	16	static
Drawable.hpp:343:18:virtual void touchgfx::Drawable::handleClickEvent(const touchgfx::ClickEvent&)	16	static
Drawable.hpp:354:18:virtual void touchgfx::Drawable::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
Drawable.hpp:442:18:virtual void touchgfx::Drawable::handleDragEvent(const touchgfx::DragEvent&)	16	static
Drawable.hpp:452:18:virtual void touchgfx::Drawable::handleTickEvent()	16	static
Drawable.hpp:488:10:bool touchgfx::Drawable::isVisible() const	16	static
Drawable.hpp:500:10:bool touchgfx::Drawable::isTouchable() const	16	static
Drawable.hpp:539:18:virtual void touchgfx::Drawable::moveTo(int16_t, int16_t)	16	static
Drawable.hpp:576:10:void touchgfx::Drawable::resetDrawChainCache()	16	static
Drawable.hpp:654:18:virtual void touchgfx::Drawable::setupDrawChain(const touchgfx::Rect&, touchgfx::Drawable**)	24	static
Screen.hpp:47:13:touchgfx::Screen::~Screen()	16	static
Screen.hpp:47:13:virtual touchgfx::Screen::~Screen()	16	static
Screen.hpp:94:18:virtual void touchgfx::Screen::setupScreen()	16	static
Screen.hpp:105:18:virtual void touchgfx::Screen::afterTransition()	16	static
Screen.hpp:116:18:virtual void touchgfx::Screen::tearDownScreen()	16	static
Screen.hpp:144:18:virtual void touchgfx::Screen::handleTickEvent()	16	static
Screen.hpp:154:18:virtual void touchgfx::Screen::handleKeyEvent(uint8_t)	16	static
Widget.hpp:47:18:virtual void touchgfx::Widget::getLastChild(int16_t, int16_t, touchgfx::Drawable**)	24	static
Widget.hpp:36:7:touchgfx::Widget::~Widget()	16	static
Widget.hpp:36:7:virtual touchgfx::Widget::~Widget()	16	static
Image.hpp:38:7:touchgfx::Image::~Image()	16	static
Image.hpp:38:7:virtual touchgfx::Image::~Image()	16	static
Container.hpp:40:7:touchgfx::Container::~Container()	16	static
Container.hpp:40:7:virtual touchgfx::Container::~Container()	16	static
View.hpp:36:7:touchgfx::View<BOOT_SCREEN_WITH_LOGOPresenter>::~View()	16	static
View.hpp:36:7:virtual touchgfx::View<BOOT_SCREEN_WITH_LOGOPresenter>::~View()	16	static
BOOT_SCREEN_WITH_LOGOViewBase.hpp:17:13:BOOT_SCREEN_WITH_LOGOViewBase::~BOOT_SCREEN_WITH_LOGOViewBase()	16	static
BOOT_SCREEN_WITH_LOGOViewBase.hpp:17:13:virtual BOOT_SCREEN_WITH_LOGOViewBase::~BOOT_SCREEN_WITH_LOGOViewBase()	16	static
BOOT_SCREEN_WITH_LOGOViewBase.hpp:29:26:FrontendApplication& BOOT_SCREEN_WITH_LOGOViewBase::application()	16	static
BOOT_SCREEN_WITH_LOGOView.hpp:16:13:BOOT_SCREEN_WITH_LOGOView::~BOOT_SCREEN_WITH_LOGOView()	16	static
BOOT_SCREEN_WITH_LOGOView.hpp:16:13:virtual BOOT_SCREEN_WITH_LOGOView::~BOOT_SCREEN_WITH_LOGOView()	16	static
BOOT_SCREEN_WITH_LOGOView.cpp:9:1:BOOT_SCREEN_WITH_LOGOView::BOOT_SCREEN_WITH_LOGOView()	16	static
BOOT_SCREEN_WITH_LOGOView.cpp:14:6:virtual void BOOT_SCREEN_WITH_LOGOView::setupScreen()	16	static
BOOT_SCREEN_WITH_LOGOView.cpp:20:6:virtual void BOOT_SCREEN_WITH_LOGOView::tearDownScreen()	16	static
BOOT_SCREEN_WITH_LOGOView.cpp:26:6:virtual void BOOT_SCREEN_WITH_LOGOView::handleTickEvent()	16	static
BOOT_SCREEN_WITH_LOGOView.cpp:31:6:virtual void BOOT_SCREEN_WITH_LOGOView::AnimationOver()	16	static
AnimatedImage.hpp:33:7:touchgfx::AnimatedImage::~AnimatedImage()	16	static
AnimatedImage.hpp:33:7:virtual touchgfx::AnimatedImage::~AnimatedImage()	16	static
Box.hpp:29:7:touchgfx::Box::~Box()	16	static
Box.hpp:29:7:virtual touchgfx::Box::~Box()	16	static
Callback.hpp:357:8:touchgfx::Callback<BOOT_SCREEN_WITH_LOGOViewBase, const touchgfx::AnimatedImage&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<BOOT_SCREEN_WITH_LOGOViewBase, const touchgfx::AnimatedImage&>::~Callback()	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AnimatedImage&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AnimatedImage&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = BOOT_SCREEN_WITH_LOGOViewBase; T1 = const touchgfx::AnimatedImage&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = BOOT_SCREEN_WITH_LOGOViewBase; T1 = const touchgfx::AnimatedImage&]	16	static
