Unicode.hpp:404:25:static touchgfx::Unicode::UnicodeChar* touchgfx::Unicode::snprintfFloat(touchgfx::Unicode::UnicodeChar*, uint16_t, const char*, float)	24	static
Drawable.hpp:62:13:touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:62:13:virtual touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:132:23:virtual touchgfx::Drawable* touchgfx::Drawable::getFirstChild()	16	static
Drawable.hpp:269:18:virtual void touchgfx::Drawable::setX(int16_t)	16	static
Drawable.hpp:281:18:virtual void touchgfx::Drawable::setY(int16_t)	16	static
Drawable.hpp:297:10:void touchgfx::Drawable::setXY(int16_t, int16_t)	16	static
Drawable.hpp:310:18:virtual void touchgfx::Drawable::setWidth(int16_t)	16	static
Drawable.hpp:322:18:virtual void touchgfx::Drawable::setHeight(int16_t)	16	static
Drawable.hpp:332:18:virtual void touchgfx::Drawable::childGeometryChanged()	16	static
Drawable.hpp:343:18:virtual void touchgfx::Drawable::handleClickEvent(const touchgfx::ClickEvent&)	16	static
Drawable.hpp:354:18:virtual void touchgfx::Drawable::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
Drawable.hpp:442:18:virtual void touchgfx::Drawable::handleDragEvent(const touchgfx::DragEvent&)	16	static
Drawable.hpp:452:18:virtual void touchgfx::Drawable::handleTickEvent()	16	static
Drawable.hpp:466:10:void touchgfx::Drawable::setVisible(bool)	16	static
Drawable.hpp:488:10:bool touchgfx::Drawable::isVisible() const	16	static
Drawable.hpp:500:10:bool touchgfx::Drawable::isTouchable() const	16	static
Drawable.hpp:539:18:virtual void touchgfx::Drawable::moveTo(int16_t, int16_t)	16	static
Drawable.hpp:576:10:void touchgfx::Drawable::resetDrawChainCache()	16	static
Drawable.hpp:654:18:virtual void touchgfx::Drawable::setupDrawChain(const touchgfx::Rect&, touchgfx::Drawable**)	24	static
Container.hpp:152:23:virtual touchgfx::Drawable* touchgfx::Container::getFirstChild()	16	static
Widget.hpp:47:18:virtual void touchgfx::Widget::getLastChild(int16_t, int16_t, touchgfx::Drawable**)	24	static
Widget.hpp:36:7:touchgfx::Widget::~Widget()	16	static
Widget.hpp:36:7:virtual touchgfx::Widget::~Widget()	16	static
AbstractButton.hpp:32:7:touchgfx::AbstractButton::~AbstractButton()	16	static
AbstractButton.hpp:32:7:virtual touchgfx::AbstractButton::~AbstractButton()	16	static
TypedText.hpp:65:13:touchgfx::TypedText::~TypedText()	16	static
TypedText.hpp:65:13:virtual touchgfx::TypedText::~TypedText()	16	static
TextArea.hpp:39:7:touchgfx::TextArea::~TextArea()	16	static
TextArea.hpp:39:7:virtual touchgfx::TextArea::~TextArea()	16	static
Container.hpp:40:7:touchgfx::Container::~Container()	16	static
Container.hpp:40:7:virtual touchgfx::Container::~Container()	16	static
HOME_SCREEN_VITAL_TEMPBase.hpp:19:13:HOME_SCREEN_VITAL_TEMPBase::~HOME_SCREEN_VITAL_TEMPBase()	16	static
HOME_SCREEN_VITAL_TEMPBase.hpp:19:13:virtual HOME_SCREEN_VITAL_TEMPBase::~HOME_SCREEN_VITAL_TEMPBase()	16	static
HOME_SCREEN_VITAL_TEMP.hpp:10:10:HOME_SCREEN_VITAL_TEMP::~HOME_SCREEN_VITAL_TEMP()	16	static
HOME_SCREEN_VITAL_TEMP.hpp:10:10:virtual HOME_SCREEN_VITAL_TEMP::~HOME_SCREEN_VITAL_TEMP()	16	static
HOME_SCREEN_VITAL_TEMP.cpp:8:1:HOME_SCREEN_VITAL_TEMP::HOME_SCREEN_VITAL_TEMP()	16	static
HOME_SCREEN_VITAL_TEMP.cpp:13:6:virtual void HOME_SCREEN_VITAL_TEMP::initialize()	16	static
HOME_SCREEN_VITAL_TEMP.cpp:20:6:virtual void HOME_SCREEN_VITAL_TEMP::handleTickEvent()	16	static
HOME_SCREEN_VITAL_TEMP.cpp:24:6:virtual void HOME_SCREEN_VITAL_TEMP::TempValue()	160	static
Button.hpp:31:7:touchgfx::Button::~Button()	16	static
Button.hpp:31:7:virtual touchgfx::Button::~Button()	16	static
TextAreaWithWildcard.hpp:34:7:touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
TextAreaWithWildcard.hpp:34:7:virtual touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
Callback.hpp:357:8:touchgfx::Callback<HOME_SCREEN_VITAL_TEMPBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<HOME_SCREEN_VITAL_TEMPBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = HOME_SCREEN_VITAL_TEMPBase; T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = HOME_SCREEN_VITAL_TEMPBase; T1 = const touchgfx::AbstractButton&]	16	static
