Types.hpp:91:5:touchgfx::colortype::colortype(uint32_t)	16	static
Bitmap.hpp:123:5:touchgfx::Bitmap::Bitmap(touchgfx::BitmapId)	16	static
Bitmap.hpp:133:14:touchgfx::BitmapId touchgfx::Bitmap::getId() const	16	static
Bitmap.hpp:144:5:touchgfx::Bitmap::operator touchgfx::BitmapId() const	16	static
Unicode.hpp:404:25:static touchgfx::Unicode::UnicodeChar* touchgfx::Unicode::snprintfFloat(touchgfx::Unicode::UnicodeChar*, uint16_t, const char*, float)	24	static
Drawable.hpp:216:10:void touchgfx::Drawable::setPosition(int16_t, int16_t, int16_t, int16_t)	24	static
Drawable.hpp:269:18:virtual void touchgfx::Drawable::setX(int16_t)	16	static
Drawable.hpp:281:18:virtual void touchgfx::Drawable::setY(int16_t)	16	static
Drawable.hpp:297:10:void touchgfx::Drawable::setXY(int16_t, int16_t)	16	static
Drawable.hpp:310:18:virtual void touchgfx::Drawable::setWidth(int16_t)	16	static
Drawable.hpp:322:18:virtual void touchgfx::Drawable::setHeight(int16_t)	16	static
Drawable.hpp:332:18:virtual void touchgfx::Drawable::childGeometryChanged()	16	static
Drawable.hpp:343:18:virtual void touchgfx::Drawable::handleClickEvent(const touchgfx::ClickEvent&)	16	static
Drawable.hpp:354:18:virtual void touchgfx::Drawable::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
Drawable.hpp:365:10:void touchgfx::Drawable::setWidthHeight(int16_t, int16_t)	16	static
Drawable.hpp:442:18:virtual void touchgfx::Drawable::handleDragEvent(const touchgfx::DragEvent&)	16	static
Drawable.hpp:466:10:void touchgfx::Drawable::setVisible(bool)	16	static
Drawable.hpp:539:18:virtual void touchgfx::Drawable::moveTo(int16_t, int16_t)	16	static
Container.hpp:152:23:virtual touchgfx::Drawable* touchgfx::Container::getFirstChild()	16	static
TextArea.hpp:330:10:void touchgfx::TextArea::setWideTextAction(touchgfx::WideTextAction)	16	static
PopupContainerBase.hpp:50:26:FrontendApplication& PopupContainerBase::application()	16	static
DefaultPopupContainer.cpp:31:1:DefaultPopupContainer::DefaultPopupContainer()	16	static
DefaultPopupContainer.cpp:36:1:DefaultPopupContainer::~DefaultPopupContainer()	16	static
DefaultPopupContainer.cpp:36:1:virtual DefaultPopupContainer::~DefaultPopupContainer()	16	static
DefaultPopupContainer.cpp:85:6:virtual void DefaultPopupContainer::initialize()	16	static
DefaultPopupContainer.cpp:90:6:virtual void DefaultPopupContainer::ClosePopup()	16	static
DefaultPopupContainer.cpp:97:6:virtual void DefaultPopupContainer::ShowPopup(uint8_t)	152	static
DefaultPopupContainer.cpp:362:6:virtual void DefaultPopupContainer::handleTickEvent()	16	static
DefaultPopupContainer.cpp:420:6:void DefaultPopupContainer::BatteryLowStatus()	72	static
DefaultPopupContainer.cpp:451:6:void DefaultPopupContainer::BLEConnStatus(uint8_t)	120	static
DefaultPopupContainer.cpp:544:6:void DefaultPopupContainer::WIFIConnStatus(uint8_t)	120	static
DefaultPopupContainer.cpp:648:6:void DefaultPopupContainer::RemoteRequestBP()	72	static
DefaultPopupContainer.cpp:678:6:void DefaultPopupContainer::RemoteRequestEKG()	72	static
DefaultPopupContainer.cpp:709:6:void DefaultPopupContainer::RequestPowerPop()	80	static
DefaultPopupContainer.cpp:746:6:void DefaultPopupContainer::HrmAbnormal(uint8_t)	112	static
DefaultPopupContainer.cpp:849:6:void DefaultPopupContainer::SPO2Abnormal(uint8_t)	112	static
DefaultPopupContainer.cpp:922:6:void DefaultPopupContainer::CBTAbnormal(float)	96	static
DefaultPopupContainer.cpp:1028:6:void DefaultPopupContainer::UpdateAvailable()	104	static
DefaultPopupContainer.cpp:1070:6:void DefaultPopupContainer::PasskeyRequestBLE()	88	static
DefaultPopupContainer.cpp:1099:6:void DefaultPopupContainer::BLEPasskey()	64	static
DefaultPopupContainer.cpp:1124:6:void DefaultPopupContainer::DeviceAlerts(uint8_t)	208	static
DefaultPopupContainer.cpp:1244:6:void DefaultPopupContainer::FallDetection()	88	static
DefaultPopupContainer.cpp:1279:6:void DefaultPopupContainer::BackgroundReading()	64	static
DefaultPopupContainer.cpp:1304:6:void DefaultPopupContainer::Pedometer60()	104	static
DefaultPopupContainer.cpp:1356:6:void DefaultPopupContainer::Pedometer100()	96	static
DefaultPopupContainer.cpp:1397:6:virtual void DefaultPopupContainer::ProceedButton()	16	static
DefaultPopupContainer.cpp:1444:6:virtual void DefaultPopupContainer::CloseButton()	16	static
DefaultPopupContainer.cpp:1498:6:virtual void DefaultPopupContainer::AnimationDone()	16	static
DefaultPopupContainer.cpp:1525:6:void DefaultPopupContainer::HidingAssets()	16	static
DefaultPopupContainer.cpp:1586:6:void DefaultPopupContainer::AlertsandNotifications()	80	static
DefaultPopupContainer.cpp:1619:6:void DefaultPopupContainer::ErrorAccelerometer()	64	static
DefaultPopupContainer.cpp:1645:6:void DefaultPopupContainer::SOSUIInitiated()	40	static
DefaultPopupContainer.cpp:1685:6:void DefaultPopupContainer::SOSWirelessInitiated()	40	static
DefaultPopupContainer.cpp:1725:6:void DefaultPopupContainer::SOSWirelessAbort()	40	static
DefaultPopupContainer.cpp:1751:6:void DefaultPopupContainer::SOSUIAbort()	72	static
DefaultPopupContainer.cpp:1778:6:void DefaultPopupContainer::SOSUITimeout()	40	static
DefaultPopupContainer.cpp:1803:6:void DefaultPopupContainer::VitalBatteryLow()	88	static
DefaultPopupContainer.cpp:1835:6:void DefaultPopupContainer::RemoteRequestSPO2()	72	static
DefaultPopupContainer.cpp:1864:6:void DefaultPopupContainer::RemoteRequestHR()	72	static
DefaultPopupContainer.cpp:1893:6:void DefaultPopupContainer::GetVitalParameterFail()	88	static
DefaultPopupContainer.cpp:1925:6:void DefaultPopupContainer::VitalDecline()	16	static
DefaultPopupContainer.cpp:1931:6:void DefaultPopupContainer::WirelessConnAvail()	16	static
DefaultPopupContainer.cpp:1964:6:void DefaultPopupContainer::WifiConnFail()	64	static
