stm32l4xx_hal.c:152:19:HAL_Init	16	static
stm32l4xx_hal.c:196:19:<PERSON><PERSON>_DeInit	8	static
stm32l4xx_hal.c:225:13:H<PERSON>_MspInit	4	static
stm32l4xx_hal.c:236:13:H<PERSON>_MspDeInit	4	static
stm32l4xx_hal.c:259:26:HAL_InitTick	24	static
stm32l4xx_hal.c:327:13:HAL_IncTick	4	static
stm32l4xx_hal.c:338:17:HAL_GetTick	4	static
stm32l4xx_hal.c:347:10:HAL_GetTickPrio	4	static
stm32l4xx_hal.c:357:19:HAL_SetTickFreq	24	static
stm32l4xx_hal.c:386:21:HAL_GetTickFreq	4	static
stm32l4xx_hal.c:402:13:<PERSON><PERSON>_<PERSON><PERSON>	24	static
stm32l4xx_hal.c:428:13:HAL_SuspendTick	4	static
stm32l4xx_hal.c:444:13:HAL_ResumeTick	4	static
stm32l4xx_hal.c:454:10:HAL_GetHalVersion	4	static
stm32l4xx_hal.c:463:10:HAL_GetREVID	4	static
stm32l4xx_hal.c:472:10:HAL_GetDEVID	4	static
stm32l4xx_hal.c:481:10:HAL_GetUIDw0	4	static
stm32l4xx_hal.c:490:10:HAL_GetUIDw1	4	static
stm32l4xx_hal.c:499:10:HAL_GetUIDw2	4	static
stm32l4xx_hal.c:528:6:HAL_DBGMCU_EnableDBGSleepMode	4	static
stm32l4xx_hal.c:537:6:HAL_DBGMCU_DisableDBGSleepMode	4	static
stm32l4xx_hal.c:546:6:HAL_DBGMCU_EnableDBGStopMode	4	static
stm32l4xx_hal.c:555:6:HAL_DBGMCU_DisableDBGStopMode	4	static
stm32l4xx_hal.c:564:6:HAL_DBGMCU_EnableDBGStandbyMode	4	static
stm32l4xx_hal.c:573:6:HAL_DBGMCU_DisableDBGStandbyMode	4	static
stm32l4xx_hal.c:606:6:HAL_SYSCFG_SRAM2Erase	4	static
stm32l4xx_hal.c:625:6:HAL_SYSCFG_EnableMemorySwappingBank	4	static
stm32l4xx_hal.c:640:6:HAL_SYSCFG_DisableMemorySwappingBank	4	static
stm32l4xx_hal.c:657:6:HAL_SYSCFG_VREFBUF_VoltageScalingConfig	16	static
stm32l4xx_hal.c:673:6:HAL_SYSCFG_VREFBUF_HighImpedanceConfig	16	static
stm32l4xx_hal.c:685:6:HAL_SYSCFG_VREFBUF_TrimmingConfig	16	static
stm32l4xx_hal.c:697:19:HAL_SYSCFG_EnableVREFBUF	16	static
stm32l4xx_hal.c:723:6:HAL_SYSCFG_DisableVREFBUF	4	static
stm32l4xx_hal.c:734:6:HAL_SYSCFG_EnableIOAnalogSwitchBooster	4	static
stm32l4xx_hal.c:744:6:HAL_SYSCFG_DisableIOAnalogSwitchBooster	4	static
