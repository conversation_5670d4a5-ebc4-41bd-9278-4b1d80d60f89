################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (9-2020-q2-update)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
S_SRCS += \
../Application/Startup/startup_stm32l4r9aiix.s 

OBJS += \
./Application/Startup/startup_stm32l4r9aiix.o 

S_DEPS += \
./Application/Startup/startup_stm32l4r9aiix.d 


# Each subdirectory must supply rules for building sources it contributes
Application/Startup/%.o: ../Application/Startup/%.s Application/Startup/subdir.mk
	arm-none-eabi-gcc -mcpu=cortex-m4 -g3 -DDEV_MODE=0 -DRAW_DATA=1 -DBOARD_1V9=0 -DBOARD_1V9A=0 -DRING_BUFFER=1 -DBOARD_1V91=0 -DBOARD_1p75=0 -DBOARD_1V92=1 -DDISABLE_BP=0 -DDISABLE_WIFI=0 -DCBT_ENABLE=0 -DCOMM_MANAGER_TEST=1 -DDISABLE_CHARGING_POPUP=0 -DDEBUB_SWO=1 -DPRIMITIVE_MANAGER_DEBUG=0 -DWIRELESS_POLL_TIMER=0 -c -I"D:/spo2_zcross/max_zcrossing/STM32CubeIDE/Application/User/gui" -I../../Middlewares/ST/touchgfx/framework/include -I../../TouchGFX/generated/fonts/include -I../../TouchGFX/generated/gui_generated/include -I../../TouchGFX/generated/images/include -I../../TouchGFX/generated/texts/include -I../../TouchGFX/gui/include -x assembler-with-cpp -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@" "$<"

