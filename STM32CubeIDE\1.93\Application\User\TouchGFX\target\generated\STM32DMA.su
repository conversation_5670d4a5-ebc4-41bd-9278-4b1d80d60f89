core_cm4.h:1679:22:void __NVIC_EnableIRQ(IRQn_Type)	16	static,ignoring_inline_asm
core_cm4.h:1717:22:void __NVIC_DisableIRQ(IRQn_Type)	16	static,ignoring_inline_asm
Types.hpp:78:5:touchgfx::colortype::colortype()	16	static
Types.hpp:114:5:touchgfx::colortype::operator uint32_t() const	16	static
DMA.hpp:61:13:touchgfx::DMA_Queue::~DMA_Queue()	16	static
DMA.hpp:61:13:virtual touchgfx::DMA_Queue::~DMA_Queue()	16	static
DMA.hpp:138:18:virtual void touchgfx::DMA_Interface::flush()	16	static
DMA.hpp:212:21:virtual touchgfx::DMAType touchgfx::DMA_Interface::getDMAType()	16	static
DMA.hpp:218:13:touchgfx::DMA_Interface::~DMA_Interface()	16	static
DMA.hpp:218:13:virtual touchgfx::DMA_Interface::~DMA_Interface()	16	static
DMA.hpp:228:5:touchgfx::DMA_Interface::DMA_Interface(touchgfx::DMA_Queue&)	16	static
HAL.hpp:118:17:static touchgfx::HAL* touchgfx::HAL::getInstance()	4	static
HAL.hpp:157:10:void touchgfx::HAL::signalDMAInterrupt()	16	static
HAL.hpp:239:17:static touchgfx::LCD& touchgfx::HAL::lcd()	4	static
STM32DMA.hpp:90:18:virtual void STM32L4DMA::signalDMAInterrupt()	16	static
STM32DMA.cpp:34:26:HAL_StatusTypeDef HAL_DMA2D_SetMode(DMA2D_HandleTypeDef*, uint32_t, uint32_t, uint32_t)	24	static
STM32DMA.cpp:47:13:void DMA2D_XferCpltCallback(DMA2D_HandleTypeDef*)	16	static
STM32DMA.cpp:52:13:void DMA2D_XferErrorCallback(DMA2D_HandleTypeDef*)	16	static
BlitOp.hpp:46:8:touchgfx::BlitOp::BlitOp()	16	static
STM32DMA.cpp:59:1:STM32L4DMA::STM32L4DMA()	24	static
STM32DMA.cpp:63:1:STM32L4DMA::~STM32L4DMA()	16	static
STM32DMA.cpp:63:1:virtual STM32L4DMA::~STM32L4DMA()	16	static
STM32DMA.cpp:69:6:virtual void STM32L4DMA::initialize()	16	static
STM32DMA.cpp:80:16:virtual touchgfx::BlitOperations STM32L4DMA::getBlitCaps()	16	static
STM32DMA.cpp:92:6:virtual void STM32L4DMA::setupDataCopy(const touchgfx::BlitOp&)	64	static
STM32DMA.cpp:193:6:virtual void STM32L4DMA::setupDataFill(const touchgfx::BlitOp&)	72	static
DMA.hpp:87:7:touchgfx::LockFreeDMA_Queue::~LockFreeDMA_Queue()	16	static
DMA.hpp:87:7:virtual touchgfx::LockFreeDMA_Queue::~LockFreeDMA_Queue()	16	static
