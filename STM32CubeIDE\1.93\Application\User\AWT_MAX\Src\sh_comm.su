sh_comm.c:84:6:sh_init_hubinterface	8	static
sh_comm.c:114:6:sh_init_hwcomm_interface	16	static
sh_comm.c:152:5:sh_hard_reset	16	static
sh_comm.c:208:13:LPM_pull_mfio_to_low_and_keep	16	static
sh_comm.c:225:13:LPM_pull_mfio_to_high	8	static
sh_comm.c:237:13:LPM_set_mfio_as_input	8	static
sh_comm.c:247:5:sh_set_ebl_mode	24	static
sh_comm.c:261:11:sh_get_ebl_mode	4	static
sh_comm.c:266:5:sh_reset_to_bootloader	16	static
sh_comm.c:289:5:in_bootldr_mode	32	static
sh_comm.c:302:5:exit_from_bootloader	32	static
sh_comm.c:313:5:stay_in_bootloader	32	static
sh_comm.c:325:13:cfg_mfio	16	static
sh_comm.c:349:5:sh_debug_reset_to_bootloader	16	static
sh_comm.c:399:5:sh_reset_to_main_app	16	static
sh_comm.c:468:5:sh_self_test	56	static
sh_comm.c:486:13:sh_get_hub_fw_version	40	static
sh_comm.c:524:13:sh_get_hub_algo_version	40	static
sh_comm.c:565:5:sh_send_raw	16	static
sh_comm.c:570:5:sh_get_log_len	48	static
sh_comm.c:592:5:sh_read_ss_log	56	static
sh_comm.c:605:5:sh_write_cmd	40	static
sh_comm.c:656:5:sh_write_cmd_with_data	32	static
sh_comm.c:666:5:sh_read_cmd	40	static
sh_comm.c:734:5:sh_get_sensorhub_status	48	static
sh_comm.c:750:5:sh_get_sensorhub_operating_mode	48	static
sh_comm.c:766:5:sh_set_sensorhub_operating_mode	24	static
sh_comm.c:777:5:sh_set_data_type	40	static
sh_comm.c:793:5:sh_get_data_type	48	static
sh_comm.c:817:5:sh_set_fifo_thresh	40	static
sh_comm.c:832:5:sh_get_fifo_thresh	48	static
sh_comm.c:850:5:sh_ss_comm_check	40	static
sh_comm.c:875:5:sh_num_avail_samples	48	static
sh_comm.c:891:5:sh_read_fifo_data	56	static
sh_comm.c:911:5:sh_set_reg	48	static
sh_comm.c:929:5:sh_get_reg	72	static
sh_comm.c:961:5:sh_sensor_enable_	32	static
sh_comm.c:973:5:sh_sensor_disable	24	static
sh_comm.c:985:5:sh_get_input_fifo_size	48	static
sh_comm.c:999:5:sh_feed_to_input_fifo	48	static
sh_comm.c:1014:5:sh_get_num_bytes_in_input_fifo	48	static
sh_comm.c:1039:5:sh_enable_algo_	32	static
sh_comm.c:1050:5:sh_disable_algo	24	static
sh_comm.c:1063:5:sh_set_algo_cfg	40	static
sh_comm.c:1077:5:sh_get_algo_cfg	48	static
sh_comm.c:1089:5:sh_get_algo_version	48	static
sh_comm.c:1118:5:sh_get_sens_cfg	48	static
sh_comm.c:1130:5:sh_set_sens_cfg	40	static
sh_comm.c:1152:5:sh_set_bootloader_delayfactor	24	static
sh_comm.c:1166:11:sh_get_bootloader_delayfactor	4	static
sh_comm.c:1172:5:sh_exit_from_bootloader	8	static
sh_comm.c:1181:5:sh_put_in_bootloader	8	static
sh_comm.c:1186:5:sh_get_usn	72	static
sh_comm.c:1207:5:sh_checkif_bootldr_mode	16	static
sh_comm.c:1220:5:sh_get_bootloader_pagesz	48	static
sh_comm.c:1247:5:sh_set_bootloader_numberofpages	40	static
sh_comm.c:1264:5:sh_set_bootloader_iv	32	static
sh_comm.c:1278:5:sh_set_bootloader_auth	32	static
sh_comm.c:1292:5:sh_set_bootloader_erase	24	static
sh_comm.c:1305:5:sh_bootloader_flashpage	24	static
sh_comm.c:1330:5:sh_get_ss_fw_version	48	static
sh_comm.c:1374:5:sh_set_report_period	40	static
sh_comm.c:1387:5:sh_spi_release	16	static
sh_comm.c:1396:5:sh_spi_use	16	static
sh_comm.c:1405:5:sh_spi_status	48	static
