cmsis_os2.c:168:22:SVC_Setup	4	static
cmsis_os2.c:185:12:osKernelInitialize	24	static,ignoring_inline_asm
cmsis_os2.c:206:12:osKernelGetInfo	24	static
cmsis_os2.c:224:17:osKernelGetState	16	static
cmsis_os2.c:249:12:osKernelStart	24	static,ignoring_inline_asm
cmsis_os2.c:272:9:osKernelLock	24	static,ignoring_inline_asm
cmsis_os2.c:299:9:osK<PERSON>lUnlock	24	static,ignoring_inline_asm
cmsis_os2.c:331:9:osKernelRestoreLock	32	static,ignoring_inline_asm
cmsis_os2.c:367:10:osKernelGetTickCount	24	static,ignoring_inline_asm
cmsis_os2.c:379:10:osKernelGetTickFreq	4	static
cmsis_os2.c:383:10:osKernelGetSysTimerCount	32	static,ignoring_inline_asm
cmsis_os2.c:400:10:osKernelGetSysTimerFreq	4	static
cmsis_os2.c:406:14:osThreadNew	72	static,ignoring_inline_asm
cmsis_os2.c:470:13:osThreadGetName	40	static,ignoring_inline_asm
cmsis_os2.c:483:14:osThreadGetId	16	static
cmsis_os2.c:491:17:osThreadGetState	40	static,ignoring_inline_asm
cmsis_os2.c:513:10:osThreadGetStackSpace	40	static,ignoring_inline_asm
cmsis_os2.c:526:12:osThreadSetPriority	40	static,ignoring_inline_asm
cmsis_os2.c:544:14:osThreadGetPriority	40	static,ignoring_inline_asm
cmsis_os2.c:557:12:osThreadYield	24	static,ignoring_inline_asm
cmsis_os2.c:570:12:osThreadSuspend	40	static,ignoring_inline_asm
cmsis_os2.c:588:12:osThreadResume	40	static,ignoring_inline_asm
cmsis_os2.c:606:18:osThreadExit	8	static
cmsis_os2.c:613:12:osThreadTerminate	40	static,ignoring_inline_asm
cmsis_os2.c:642:10:osThreadGetCount	24	static,ignoring_inline_asm
cmsis_os2.c:654:10:osThreadEnumerate	40	static,ignoring_inline_asm
cmsis_os2.c:682:10:osThreadFlagsSet	48	static,ignoring_inline_asm
cmsis_os2.c:710:10:osThreadFlagsClear	40	static,ignoring_inline_asm
cmsis_os2.c:740:10:osThreadFlagsGet	32	static,ignoring_inline_asm
cmsis_os2.c:758:10:osThreadFlagsWait	64	static,ignoring_inline_asm
cmsis_os2.c:833:12:osDelay	32	static,ignoring_inline_asm
cmsis_os2.c:850:12:osDelayUntil	40	static,ignoring_inline_asm
cmsis_os2.c:880:13:TimerCallback	24	static
cmsis_os2.c:890:13:osTimerNew	64	static,ignoring_inline_asm
cmsis_os2.c:948:13:osTimerGetName	40	static,ignoring_inline_asm
cmsis_os2.c:961:12:osTimerStart	48	static,ignoring_inline_asm
cmsis_os2.c:982:12:osTimerStop	48	static,ignoring_inline_asm
cmsis_os2.c:1008:10:osTimerIsRunning	40	static,ignoring_inline_asm
cmsis_os2.c:1021:12:osTimerDelete	48	static,ignoring_inline_asm
cmsis_os2.c:1052:18:osEventFlagsNew	40	static,ignoring_inline_asm
cmsis_os2.c:1088:10:osEventFlagsSet	40	static,ignoring_inline_asm
cmsis_os2.c:1113:10:osEventFlagsClear	40	static,ignoring_inline_asm
cmsis_os2.c:1134:10:osEventFlagsGet	40	static,ignoring_inline_asm
cmsis_os2.c:1151:10:osEventFlagsWait	64	static,ignoring_inline_asm
cmsis_os2.c:1201:12:osEventFlagsDelete	40	static,ignoring_inline_asm
cmsis_os2.c:1225:13:osMutexNew	48	static,ignoring_inline_asm
cmsis_os2.c:1304:12:osMutexAcquire	40	static,ignoring_inline_asm
cmsis_os2.c:1345:12:osMutexRelease	40	static,ignoring_inline_asm
cmsis_os2.c:1378:14:osMutexGetOwner	40	static,ignoring_inline_asm
cmsis_os2.c:1393:12:osMutexDelete	40	static,ignoring_inline_asm
cmsis_os2.c:1422:17:osSemaphoreNew	56	static,ignoring_inline_asm
cmsis_os2.c:1489:12:osSemaphoreAcquire	40	static,ignoring_inline_asm
cmsis_os2.c:1526:12:osSemaphoreRelease	40	static,ignoring_inline_asm
cmsis_os2.c:1554:10:osSemaphoreGetCount	40	static,ignoring_inline_asm
cmsis_os2.c:1570:12:osSemaphoreDelete	40	static,ignoring_inline_asm
cmsis_os2.c:1598:20:osMessageQueueNew	56	static,ignoring_inline_asm
cmsis_os2.c:1651:12:osMessageQueuePut	48	static,ignoring_inline_asm
cmsis_os2.c:1692:12:osMessageQueueGet	48	static,ignoring_inline_asm
cmsis_os2.c:1733:10:osMessageQueueGetCapacity	24	static
cmsis_os2.c:1747:10:osMessageQueueGetMsgSize	24	static
cmsis_os2.c:1761:10:osMessageQueueGetCount	40	static,ignoring_inline_asm
cmsis_os2.c:1778:10:osMessageQueueGetSpace	56	static,ignoring_inline_asm
cmsis_os2.c:1801:12:osMessageQueueReset	40	static,ignoring_inline_asm
cmsis_os2.c:1819:12:osMessageQueueDelete	40	static,ignoring_inline_asm
cmsis_os2.c:1858:13:vApplicationIdleHook	4	static
cmsis_os2.c:1886:13:vApplicationStackOverflowHook	16	static
cmsis_os2.c:1910:6:vApplicationGetIdleTaskMemory	24	static
cmsis_os2.c:1920:6:vApplicationGetTimerTaskMemory	24	static
