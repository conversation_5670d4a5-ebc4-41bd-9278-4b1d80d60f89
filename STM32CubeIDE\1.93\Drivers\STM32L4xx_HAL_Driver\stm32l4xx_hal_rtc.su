stm32l4xx_hal_rtc.c:246:19:H<PERSON>_RTC_Init	24	static
stm32l4xx_hal_rtc.c:379:19:H<PERSON>_RTC_DeInit	24	static
stm32l4xx_hal_rtc.c:695:13:<PERSON><PERSON>_RTC_MspInit	16	static
stm32l4xx_hal_rtc.c:710:13:H<PERSON>_RTC_MspDeInit	16	static
stm32l4xx_hal_rtc.c:1442:19:HAL_RTC_SetTime	40	static
stm32l4xx_hal_rtc.c:1552:19:HAL_RTC_GetTime	32	static
stm32l4xx_hal_rtc.c:1596:19:H<PERSON>_RTC_SetDate	40	static
stm32l4xx_hal_rtc.c:1680:19:HAL_RTC_GetDate	32	static
stm32l4xx_hal_rtc.c:1734:19:<PERSON><PERSON>_<PERSON><PERSON>_SetAlarm	48	static
stm32l4xx_hal_rtc.c:1921:19:HAL_RTC_SetAlarm_IT	48	static
stm32l4xx_hal_rtc.c:2111:19:H<PERSON>_RTC_DeactivateAlarm	24	static
stm32l4xx_hal_rtc.c:2205:19:HAL_RTC_GetAlarm	32	static
stm32l4xx_hal_rtc.c:2265:6:HAL_RTC_AlarmIRQHandler	16	static
stm32l4xx_hal_rtc.c:2346:13:HAL_RTC_AlarmAEventCallback	16	static
stm32l4xx_hal_rtc.c:2362:19:HAL_RTC_PollForAlarmAEvent	24	static
stm32l4xx_hal_rtc.c:2421:19:HAL_RTC_WaitForSynchro	24	static
stm32l4xx_hal_rtc.c:2473:21:HAL_RTC_GetState	16	static
stm32l4xx_hal_rtc.c:2497:19:RTC_EnterInitMode	24	static
stm32l4xx_hal_rtc.c:2547:19:RTC_ExitInitMode	24	static
stm32l4xx_hal_rtc.c:2591:9:RTC_ByteToBcd2	24	static
stm32l4xx_hal_rtc.c:2610:9:RTC_Bcd2ToByte	24	static
