tasks.c:578:15:xTaskCreateStatic	64	static
tasks.c:730:13:xTaskCreate	56	static
tasks.c:821:13:prvInitialiseNewTask	40	static
tasks.c:1072:13:prvAddNewTaskToReadyList	16	static,ignoring_inline_asm
tasks.c:1157:7:vTask<PERSON><PERSON><PERSON>	24	static,ignoring_inline_asm
tasks.c:1249:7:vTaskDelayUntil	48	static,ignoring_inline_asm
tasks.c:1333:7:vTaskDelay	24	static,ignoring_inline_asm
tasks.c:1378:13:eTaskGetState	40	static
tasks.c:1470:14:uxTaskPriorityGet	24	static
tasks.c:1492:14:uxTaskPriorityGetFromISR	40	static,ignoring_inline_asm
tasks.c:1532:7:vTaskPrioritySet	40	static,ignoring_inline_asm
tasks.c:1696:7:vTaskSuspend	24	static,ignoring_inline_asm
tasks.c:1797:20:prvTaskIsTaskSuspended	32	static
tasks.c:1843:7:vTask<PERSON><PERSON><PERSON>	24	static,ignoring_inline_asm
tasks.c:1897:13:xTaskResumeFromISR	48	static,ignoring_inline_asm
tasks.c:1967:6:vTaskStartScheduler	48	static,ignoring_inline_asm
tasks.c:2089:6:vTaskEndScheduler	16	static,ignoring_inline_asm
tasks.c:2100:6:vTaskSuspendAll	4	static,ignoring_inline_asm
tasks.c:2174:12:xTaskResumeAll	24	static,ignoring_inline_asm
tasks.c:2284:12:xTaskGetTickCount	16	static
tasks.c:2299:12:xTaskGetTickCountFromISR	16	static
tasks.c:2330:13:uxTaskGetNumberOfTasks	4	static
tasks.c:2338:7:pcTaskGetName	24	static
tasks.c:2485:14:uxTaskGetSystemState	32	static
tasks.c:2665:12:xTaskIncrementTick	32	static
tasks.c:2839:7:vTaskSetApplicationTaskTag	24	static
tasks.c:2868:21:xTaskGetApplicationTaskTag	24	static
tasks.c:2892:21:xTaskGetApplicationTaskTagFromISR	40	static,ignoring_inline_asm
tasks.c:2917:13:xTaskCallApplicationTaskHook	24	static
tasks.c:2947:6:vTaskSwitchContext	24	static
tasks.c:3020:6:vTaskPlaceOnEventList	24	static
tasks.c:3037:6:vTaskPlaceOnUnorderedEventList	32	static
tasks.c:3063:7:vTaskPlaceOnEventListRestricted	32	static
tasks.c:3094:12:xTaskRemoveFromEventList	32	static
tasks.c:3162:6:vTaskRemoveFromUnorderedEventList	32	static
tasks.c:3196:6:vTaskSetTimeOutState	24	static
tasks.c:3208:6:vTaskInternalSetTimeOutState	16	static
tasks.c:3216:12:xTaskCheckForTimeOut	40	static
tasks.c:3279:6:vTaskMissedYield	4	static
tasks.c:3287:14:uxTaskGetTaskNumber	24	static
tasks.c:3310:7:vTaskSetTaskNumber	24	static
tasks.c:3334:8:prvIdleTask	16	static,ignoring_inline_asm
tasks.c:3546:13:prvInitialiseTaskLists	16	static
tasks.c:3578:13:prvCheckTasksWaitingTermination	16	static
tasks.c:3609:7:vTaskGetInfo	32	static
tasks.c:3705:21:prvListTasksWithinSingleList	48	static
tasks.c:3738:32:prvTaskCheckFreeStackSpace	24	static
tasks.c:3798:14:uxTaskGetStackHighWaterMark	32	static
tasks.c:3826:14:prvDeleteTCB	24	static
tasks.c:3880:13:prvResetNextTaskUnblockTime	16	static
tasks.c:3906:15:xTaskGetCurrentTaskHandle	16	static
tasks.c:3923:13:xTaskGetSchedulerState	16	static
tasks.c:3951:13:xTaskPriorityInherit	24	static
tasks.c:4038:13:xTaskPriorityDisinherit	32	static
tasks.c:4118:7:vTaskPriorityDisinheritAfterTimeout	40	static
tasks.c:4533:12:uxTaskResetEventItemValue	16	static
tasks.c:4549:15:pvTaskIncrementMutexHeldCount	4	static
tasks.c:4566:11:ulTaskNotifyTake	24	static,ignoring_inline_asm
tasks.c:4634:13:xTaskNotifyWait	32	static,ignoring_inline_asm
tasks.c:4714:13:xTaskGenericNotify	48	static,ignoring_inline_asm
tasks.c:4828:13:xTaskGenericNotifyFromISR	64	static,ignoring_inline_asm
tasks.c:4957:7:vTaskNotifyGiveFromISR	48	static,ignoring_inline_asm
tasks.c:5044:13:xTaskNotifyStateClear	24	static
tasks.c:5081:13:prvAddCurrentTaskToDelayedList	24	static
