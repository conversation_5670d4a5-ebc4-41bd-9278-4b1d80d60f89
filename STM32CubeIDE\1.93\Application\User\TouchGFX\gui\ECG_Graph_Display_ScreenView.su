Drawable.hpp:62:13:touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:62:13:virtual touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:132:23:virtual touchgfx::Drawable* touchgfx::Drawable::getFirstChild()	16	static
Drawable.hpp:216:10:void touchgfx::Drawable::setPosition(int16_t, int16_t, int16_t, int16_t)	24	static
Drawable.hpp:269:18:virtual void touchgfx::Drawable::setX(int16_t)	16	static
Drawable.hpp:281:18:virtual void touchgfx::Drawable::setY(int16_t)	16	static
Drawable.hpp:297:10:void touchgfx::Drawable::setXY(int16_t, int16_t)	16	static
Drawable.hpp:310:18:virtual void touchgfx::Drawable::setWidth(int16_t)	16	static
Drawable.hpp:322:18:virtual void touchgfx::Drawable::setHeight(int16_t)	16	static
Drawable.hpp:332:18:virtual void touchgfx::Drawable::childGeometryChanged()	16	static
Drawable.hpp:343:18:virtual void touchgfx::Drawable::handleClickEvent(const touchgfx::ClickEvent&)	16	static
Drawable.hpp:354:18:virtual void touchgfx::Drawable::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
Drawable.hpp:365:10:void touchgfx::Drawable::setWidthHeight(int16_t, int16_t)	16	static
Drawable.hpp:442:18:virtual void touchgfx::Drawable::handleDragEvent(const touchgfx::DragEvent&)	16	static
Drawable.hpp:452:18:virtual void touchgfx::Drawable::handleTickEvent()	16	static
Drawable.hpp:488:10:bool touchgfx::Drawable::isVisible() const	16	static
Drawable.hpp:500:10:bool touchgfx::Drawable::isTouchable() const	16	static
Drawable.hpp:539:18:virtual void touchgfx::Drawable::moveTo(int16_t, int16_t)	16	static
Drawable.hpp:576:10:void touchgfx::Drawable::resetDrawChainCache()	16	static
Drawable.hpp:654:18:virtual void touchgfx::Drawable::setupDrawChain(const touchgfx::Rect&, touchgfx::Drawable**)	24	static
Container.hpp:152:23:virtual touchgfx::Drawable* touchgfx::Container::getFirstChild()	16	static
Screen.hpp:47:13:touchgfx::Screen::~Screen()	16	static
Screen.hpp:47:13:virtual touchgfx::Screen::~Screen()	16	static
Screen.hpp:94:18:virtual void touchgfx::Screen::setupScreen()	16	static
Screen.hpp:105:18:virtual void touchgfx::Screen::afterTransition()	16	static
Screen.hpp:116:18:virtual void touchgfx::Screen::tearDownScreen()	16	static
Screen.hpp:144:18:virtual void touchgfx::Screen::handleTickEvent()	16	static
Screen.hpp:154:18:virtual void touchgfx::Screen::handleKeyEvent(uint8_t)	16	static
Screen.hpp:209:10:void touchgfx::Screen::addPopup(touchgfx::Container&)	16	static
Screen.hpp:219:10:void touchgfx::Screen::addStatusBar(touchgfx::Container&)	16	static
Widget.hpp:47:18:virtual void touchgfx::Widget::getLastChild(int16_t, int16_t, touchgfx::Drawable**)	24	static
Widget.hpp:36:7:touchgfx::Widget::~Widget()	16	static
Widget.hpp:36:7:virtual touchgfx::Widget::~Widget()	16	static
AbstractButton.hpp:32:7:touchgfx::AbstractButton::~AbstractButton()	16	static
AbstractButton.hpp:32:7:virtual touchgfx::AbstractButton::~AbstractButton()	16	static
TypedText.hpp:65:13:touchgfx::TypedText::~TypedText()	16	static
TypedText.hpp:65:13:virtual touchgfx::TypedText::~TypedText()	16	static
TextArea.hpp:39:7:touchgfx::TextArea::~TextArea()	16	static
TextArea.hpp:39:7:virtual touchgfx::TextArea::~TextArea()	16	static
Image.hpp:38:7:touchgfx::Image::~Image()	16	static
Image.hpp:38:7:virtual touchgfx::Image::~Image()	16	static
Container.hpp:40:7:touchgfx::Container::~Container()	16	static
Container.hpp:40:7:virtual touchgfx::Container::~Container()	16	static
PopupBase.h:21:2:PopupBase::PopupBase(touchgfx::Screen*)	16	static
PopupBase.h:26:10:PopupBase::~PopupBase()	16	static
PopupBase.h:26:10:virtual PopupBase::~PopupBase()	16	static
Battery_BT_containerBase.hpp:15:13:Battery_BT_containerBase::~Battery_BT_containerBase()	16	static
Battery_BT_containerBase.hpp:15:13:virtual Battery_BT_containerBase::~Battery_BT_containerBase()	16	static
Battery_BT_container.hpp:10:10:Battery_BT_container::~Battery_BT_container()	16	static
Battery_BT_container.hpp:10:10:virtual Battery_BT_container::~Battery_BT_container()	16	static
StatusBarBase.h:19:2:StatusbarBase::StatusbarBase(touchgfx::Screen*)	16	static
StatusBarBase.h:25:10:StatusbarBase::~StatusbarBase()	16	static
StatusBarBase.h:25:10:virtual StatusbarBase::~StatusbarBase()	16	static
AbstractPainter.hpp:51:13:touchgfx::AbstractPainter::~AbstractPainter()	16	static
AbstractPainter.hpp:51:13:virtual touchgfx::AbstractPainter::~AbstractPainter()	16	static
CanvasWidget.hpp:60:18:virtual void touchgfx::CanvasWidget::setAlpha(uint8_t)	16	static
CanvasWidget.hpp:66:21:virtual uint8_t touchgfx::CanvasWidget::getAlpha() const	16	static
AbstractPainterRGB565.hpp:34:7:touchgfx::AbstractPainterRGB565::~AbstractPainterRGB565()	16	static
AbstractPainterRGB565.hpp:34:7:virtual touchgfx::AbstractPainterRGB565::~AbstractPainterRGB565()	16	static
View.hpp:36:7:touchgfx::View<ECG_Graph_Display_ScreenPresenter>::~View()	16	static
View.hpp:36:7:virtual touchgfx::View<ECG_Graph_Display_ScreenPresenter>::~View()	16	static
ECG_Graph_Display_ScreenViewBase.hpp:32:13:ECG_Graph_Display_ScreenViewBase::~ECG_Graph_Display_ScreenViewBase()	16	static
ECG_Graph_Display_ScreenViewBase.hpp:32:13:virtual ECG_Graph_Display_ScreenViewBase::~ECG_Graph_Display_ScreenViewBase()	16	static
ECG_Graph_Display_ScreenView.hpp:12:10:ECG_Graph_Display_ScreenView::~ECG_Graph_Display_ScreenView()	16	static
ECG_Graph_Display_ScreenView.hpp:12:10:virtual ECG_Graph_Display_ScreenView::~ECG_Graph_Display_ScreenView()	16	static
ECG_Graph_Display_ScreenView.cpp:14:10:static uint32_t ECG_Graph_Display_ScreenView::GetActive()	4	static
ECG_Graph_Display_ScreenView.cpp:19:1:ECG_Graph_Display_ScreenView::ECG_Graph_Display_ScreenView()	16	static
ECG_Graph_Display_ScreenView.cpp:24:6:virtual void ECG_Graph_Display_ScreenView::setupScreen()	24	static
ECG_Graph_Display_ScreenView.cpp:57:6:virtual void ECG_Graph_Display_ScreenView::tearDownScreen()	16	static
ECG_Graph_Display_ScreenView.cpp:64:6:virtual void ECG_Graph_Display_ScreenView::handleTickEvent()	16	static
Button.hpp:31:7:touchgfx::Button::~Button()	16	static
Button.hpp:31:7:virtual touchgfx::Button::~Button()	16	static
TextAreaWithWildcard.hpp:34:7:touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
TextAreaWithWildcard.hpp:34:7:virtual touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
AnimatedImage.hpp:33:7:touchgfx::AnimatedImage::~AnimatedImage()	16	static
AnimatedImage.hpp:33:7:virtual touchgfx::AnimatedImage::~AnimatedImage()	16	static
Box.hpp:29:7:touchgfx::Box::~Box()	16	static
Box.hpp:29:7:virtual touchgfx::Box::~Box()	16	static
ScalableImage.hpp:35:7:touchgfx::ScalableImage::~ScalableImage()	16	static
ScalableImage.hpp:35:7:virtual touchgfx::ScalableImage::~ScalableImage()	16	static
AbstractDataGraph.hpp:33:7:touchgfx::AbstractDataGraph::~AbstractDataGraph()	16	static
AbstractDataGraph.hpp:33:7:virtual touchgfx::AbstractDataGraph::~AbstractDataGraph()	16	static
AbstractDataGraph.hpp:895:7:touchgfx::AbstractDataGraphWithY::~AbstractDataGraphWithY()	16	static
AbstractDataGraph.hpp:895:7:virtual touchgfx::AbstractDataGraphWithY::~AbstractDataGraphWithY()	16	static
GraphWrapAndOverwrite.hpp:31:7:touchgfx::DataGraphWrapAndOverwrite::~DataGraphWrapAndOverwrite()	16	static
GraphWrapAndOverwrite.hpp:31:7:virtual touchgfx::DataGraphWrapAndOverwrite::~DataGraphWrapAndOverwrite()	16	static
GraphWrapAndOverwrite.hpp:56:7:touchgfx::GraphWrapAndOverwrite<200>::~GraphWrapAndOverwrite()	16	static
GraphWrapAndOverwrite.hpp:56:7:virtual touchgfx::GraphWrapAndOverwrite<200>::~GraphWrapAndOverwrite()	16	static
MoveAnimator.hpp:37:7:touchgfx::MoveAnimator<touchgfx::GraphWrapAndOverwrite<200> >::~MoveAnimator()	16	static
MoveAnimator.hpp:37:7:virtual touchgfx::MoveAnimator<touchgfx::GraphWrapAndOverwrite<200> >::~MoveAnimator()	16	static
CanvasWidget.hpp:33:7:touchgfx::CanvasWidget::~CanvasWidget()	16	static
CanvasWidget.hpp:33:7:virtual touchgfx::CanvasWidget::~CanvasWidget()	16	static
GraphElements.hpp:30:7:touchgfx::AbstractGraphElement::~AbstractGraphElement()	16	static
GraphElements.hpp:30:7:virtual touchgfx::AbstractGraphElement::~AbstractGraphElement()	16	static
GraphElements.hpp:467:7:touchgfx::GraphElementLine::~GraphElementLine()	16	static
GraphElements.hpp:467:7:virtual touchgfx::GraphElementLine::~GraphElementLine()	16	static
PainterRGB565.hpp:32:7:touchgfx::PainterRGB565::~PainterRGB565()	16	static
PainterRGB565.hpp:32:7:virtual touchgfx::PainterRGB565::~PainterRGB565()	16	static
Callback.hpp:357:8:touchgfx::Callback<ECG_Graph_Display_ScreenViewBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<ECG_Graph_Display_ScreenViewBase, const touchgfx::AbstractButton&>::~Callback()	16	static
Callback.hpp:357:8:touchgfx::Callback<ECG_Graph_Display_ScreenViewBase, const touchgfx::AnimatedImage&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<ECG_Graph_Display_ScreenViewBase, const touchgfx::AnimatedImage&>::~Callback()	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AnimatedImage&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::AnimatedImage&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = ECG_Graph_Display_ScreenViewBase; T1 = const touchgfx::AnimatedImage&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = ECG_Graph_Display_ScreenViewBase; T1 = const touchgfx::AnimatedImage&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = ECG_Graph_Display_ScreenViewBase; T1 = const touchgfx::AbstractButton&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = ECG_Graph_Display_ScreenViewBase; T1 = const touchgfx::AbstractButton&]	16	static
MoveAnimator.hpp:169:18:void touchgfx::MoveAnimator<T>::handleTickEvent() [with T = touchgfx::GraphWrapAndOverwrite<200>]	16	static
MoveAnimator.hpp:88:18:void touchgfx::MoveAnimator<T>::setMoveAnimationDelay(uint16_t) [with T = touchgfx::GraphWrapAndOverwrite<200>]	16	static
MoveAnimator.hpp:100:22:uint16_t touchgfx::MoveAnimator<T>::getMoveAnimationDelay() const [with T = touchgfx::GraphWrapAndOverwrite<200>]	16	static
MoveAnimator.hpp:178:10:void touchgfx::MoveAnimator<T>::nextMoveAnimationStep() [with T = touchgfx::GraphWrapAndOverwrite<200>]	32	static
