#include "FreeRTOS.h"
#include "task.h"
#include "AWT_parameterRequest.h"
#include "database.h"
#include "AS7050_sensor.h"
#include "AWT_SENSORS_DATA.h"
#include "event_handler.h"
#include "MAX_HRM.h"
#include "MAX_SPO2.h"

/********************* HRM Parameter Function *******************************/

void HRMParameter(sensor_callback HRM_Callback)
{
	uint8_t Result = VITAL_FAIL;
	SENSOR_DATA_STRUCT Requested_Parameter;
	Requested_Parameter.VITAL_REQUESTED_PARAM = Heart_Rate;

		enableSensor();
#if !MAX_SENSOR
		Result = As7050_Hrm(&Requested_Parameter,HRM_Callback,VITAL_MODE_SINGLE);
#else
		Result =  MAX_HRM(&Requested_Parameter,HRM_Callback);
#endif
		disableSensor();

	Requested_Parameter.UNIX_TIME = Get_Current_Unix_Time();
	if(Result == VITAL_SUCCESS)
	{
		UPDATE_DATABASE_WITH_TIME(&Requested_Parameter);
		//End state of HR state machine is completed
		HRM_Callback(END_STATE);
	}
	else
	{
		//Triggering error event to sensor task
		HRM_Callback(ERR_STATE);
	}


}


/********************* SPO2 Parameter Function *******************************/

void SPO2Parameter(sensor_callback SPO2_Callback)
{
	uint8_t Result = VITAL_FAIL;
	SENSOR_DATA_STRUCT Requested_Parameter;
	Requested_Parameter.VITAL_REQUESTED_PARAM = SpO2;

	// Test BLE transmission before SPO2 measurement
	Test_SPO2_BLE_Transmission();

		enableSensor();
#if !MAX_SENSOR
		Result = As7050_Spo2(&Requested_Parameter,SPO2_Callback,VITAL_MODE_SINGLE);
#else
		Result = MAX_SPO2(&Requested_Parameter,SPO2_Callback);
#endif
		disableSensor();

	Requested_Parameter.UNIX_TIME = Get_Current_Unix_Time();
	if(Result == VITAL_SUCCESS)
	{
		UPDATE_DATABASE_WITH_TIME(&Requested_Parameter);
#if RAW_DATA
		UPDATE_DATABASE_WITH_RAWDATA(&Requested_Parameter);
#endif
		//End state of SPO2 state machine is completed
		SPO2_Callback(END_STATE);
	}
	else
	{
		//Triggering error event to sensor task
		SPO2_Callback(ERR_STATE);
	}

}



/********************* BP Parameter Function *******************************/

void BpParameter(sensor_callback BP_Callback)
{
	_Bool Result = VITAL_FAIL;
	SENSOR_DATA_STRUCT Requested_Parameter;
	Requested_Parameter.VITAL_REQUESTED_PARAM = BP;

	enableSensor();
	Result = As7050_BP(&Requested_Parameter,BP_Callback);
	disableSensor();

#if RAW_DATA
	UPDATE_DATABASE_WITH_RAWDATA(&Requested_Parameter);
#endif
	Requested_Parameter.UNIX_TIME = Get_Current_Unix_Time();
	if(Result == VITAL_SUCCESS)
	{
		UPDATE_DATABASE_WITH_TIME(&Requested_Parameter);
		//End state of BP state machine is completed
		BP_Callback(END_STATE);
		//xEventGroupSetBits(xSensorTskEvnt,END_STATE);
	}
	else
	{
		//Triggering error event to sensor task
		BP_Callback(ERR_STATE);
		//xEventGroupSetBits(xSensorTskEvnt,ERR_STATE);
	}


}

/********************* ECG Parameter Function *******************************/

void ECGParameter(sensor_callback ECG_Callback)
{
	_Bool Result = VITAL_FAIL;
	SENSOR_DATA_STRUCT Requested_Parameter;
	Requested_Parameter.VITAL_REQUESTED_PARAM = ECG;

	enableSensor();
	Result = As7050_Ecg(&Requested_Parameter,ECG_Callback);
	disableSensor();

#if RAW_DATA
	UPDATE_DATABASE_WITH_RAWDATA(&Requested_Parameter);
#endif
	Requested_Parameter.UNIX_TIME = Get_Current_Unix_Time();
	if(Result == VITAL_SUCCESS)
	{
		UPDATE_DATABASE_WITH_TIME(&Requested_Parameter);
		//End state of ECG state machine is completed
		ECG_Callback(END_STATE);
		//xEventGroupSetBits(xSensorTskEvnt,END_STATE);
	}
	else
	{
		//Triggering error event to sensor task
		ECG_Callback(ERR_STATE);
		//xEventGroupSetBits(xSensorTskEvnt,ERR_STATE);
	}
}

void BTParameter(sensor_callback BT_Callback)
{

	//START STATE of CBT state machine completed
	BT_Callback(STT_STATE);
	//xEventGroupSetBits(xSensorTskEvnt,STT_STATE);

	_Bool Result = VITAL_FAIL;
	SENSOR_DATA_STRUCT Requested_Parameter;
	Requested_Parameter.VITAL_REQUESTED_PARAM = Body_Temperature;

	enableSensor();
	Result = 0;//BodyTemperature(&Requested_Parameter,BT_Callback);
	disableSensor();

	Requested_Parameter.UNIX_TIME = Get_Current_Unix_Time();
	if(Result == VITAL_SUCCESS)
	{
		UPDATE_DATABASE_WITH_TIME(&Requested_Parameter);
		//End state of CBT state machine is completed
		BT_Callback(END_STATE);
		//xEventGroupSetBits(xSensorTskEvnt,END_STATE);
	}
	else
	{
		//Triggering error event to sensor task
		BT_Callback(ERR_STATE);
		//xEventGroupSetBits(xSensorTskEvnt,ERR_STATE);
	}
}

/********************* GSR Parameter Function *******************************/
void GSRParameter(sensor_callback GSR_Callback)
{
	_Bool Result = VITAL_FAIL;
	SENSOR_DATA_STRUCT Requested_Parameter;
	Requested_Parameter.VITAL_REQUESTED_PARAM = GSR;
	enableSensor();
	Result = 0;//AS_7050_GSR(&Requested_Parameter,GSR_Callback);
	disableSensor();
	Requested_Parameter.UNIX_TIME = Get_Current_Unix_Time();
	if(Result == VITAL_SUCCESS)
	{
		//End state of GSR state machine is completed
		GSR_Callback(END_STATE);
		//xEventGroupSetBits(xSensorTskEvnt,END_STATE);
	}
	else
	{
		//Triggering error event to sensor task
		GSR_Callback(ERR_STATE);
		//xEventGroupSetBits(xSensorTskEvnt,ERR_STATE);
	}
}
