TouchController.hpp:30:13:touchgfx::TouchController::~Touch<PERSON><PERSON>roll<PERSON>()	16	static
TouchController.hpp:30:13:virtual touchgfx::TouchController::~TouchController()	16	static
STM32TouchController.cpp:41:6:virtual void STM32TouchController::init()	16	static
STM32TouchController.cpp:55:6:virtual bool STM32TouchController::sampleTouch(int32_t&, int32_t&)	32	static
STM32TouchController.cpp:81:6:void StartTouchTask(void*)	24	static
STM32TouchController.cpp:125:6:void TS_IO_Init()	4	static
STM32TouchController.cpp:139:6:void TS_IO_Write(uint8_t, uint8_t, uint8_t)	32	static
STM32TouchController.cpp:151:9:uint8_t TS_IO_Read(uint8_t, uint8_t)	40	static
STM32TouchController.cpp:167:10:uint16_t TS_IO_ReadMultiple(uint8_t, uint8_t, uint8_t*, uint16_t)	32	static
STM32TouchController.cpp:182:6:void TS_IO_WriteMultiple(uint8_t, uint8_t, uint8_t*, uint16_t)	32	static
STM32TouchController.hpp:33:7:STM32TouchController::~STM32TouchController()	16	static
STM32TouchController.hpp:33:7:virtual STM32TouchController::~STM32TouchController()	16	static
