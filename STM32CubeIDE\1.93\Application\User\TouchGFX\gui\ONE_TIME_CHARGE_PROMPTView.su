Types.hpp:140:5:touchgfx::Rect::Rect(int16_t, int16_t, int16_t, int16_t)	24	static
Drawable.hpp:62:13:touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:62:13:virtual touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:132:23:virtual touchgfx::Drawable* touchgfx::Drawable::getFirstChild()	16	static
Drawable.hpp:269:18:virtual void touchgfx::Drawable::setX(int16_t)	16	static
Drawable.hpp:281:18:virtual void touchgfx::Drawable::setY(int16_t)	16	static
Drawable.hpp:310:18:virtual void touchgfx::Drawable::setWidth(int16_t)	16	static
Drawable.hpp:322:18:virtual void touchgfx::Drawable::setHeight(int16_t)	16	static
Drawable.hpp:332:18:virtual void touchgfx::Drawable::childGeometryChanged()	16	static
Drawable.hpp:343:18:virtual void touchgfx::Drawable::handleClickEvent(const touchgfx::ClickEvent&)	16	static
Drawable.hpp:354:18:virtual void touchgfx::Drawable::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
Drawable.hpp:442:18:virtual void touchgfx::Drawable::handleDragEvent(const touchgfx::DragEvent&)	16	static
Drawable.hpp:452:18:virtual void touchgfx::Drawable::handleTickEvent()	16	static
Drawable.hpp:488:10:bool touchgfx::Drawable::isVisible() const	16	static
Drawable.hpp:500:10:bool touchgfx::Drawable::isTouchable() const	16	static
Drawable.hpp:539:18:virtual void touchgfx::Drawable::moveTo(int16_t, int16_t)	16	static
Drawable.hpp:576:10:void touchgfx::Drawable::resetDrawChainCache()	16	static
Drawable.hpp:654:18:virtual void touchgfx::Drawable::setupDrawChain(const touchgfx::Rect&, touchgfx::Drawable**)	24	static
Screen.hpp:47:13:touchgfx::Screen::~Screen()	16	static
Screen.hpp:47:13:virtual touchgfx::Screen::~Screen()	16	static
Screen.hpp:94:18:virtual void touchgfx::Screen::setupScreen()	16	static
Screen.hpp:105:18:virtual void touchgfx::Screen::afterTransition()	16	static
Screen.hpp:116:18:virtual void touchgfx::Screen::tearDownScreen()	16	static
Screen.hpp:144:18:virtual void touchgfx::Screen::handleTickEvent()	16	static
Screen.hpp:154:18:virtual void touchgfx::Screen::handleKeyEvent(uint8_t)	16	static
Widget.hpp:47:18:virtual void touchgfx::Widget::getLastChild(int16_t, int16_t, touchgfx::Drawable**)	24	static
Widget.hpp:36:7:touchgfx::Widget::~Widget()	16	static
Widget.hpp:36:7:virtual touchgfx::Widget::~Widget()	16	static
TypedText.hpp:65:13:touchgfx::TypedText::~TypedText()	16	static
TypedText.hpp:65:13:virtual touchgfx::TypedText::~TypedText()	16	static
TextArea.hpp:47:18:virtual touchgfx::Rect touchgfx::TextArea::getSolidRect() const	24	static
TextArea.hpp:76:10:void touchgfx::TextArea::setAlpha(uint8_t)	16	static
TextArea.hpp:98:18:virtual void touchgfx::TextArea::setBaselineY(int16_t)	48	static
TextArea.hpp:114:18:virtual void touchgfx::TextArea::setXBaselineY(int16_t, int16_t)	16	static
TypedText.hpp:41:7:constexpr touchgfx::TypedText::TypedText(const touchgfx::TypedText&)	16	static
TextArea.hpp:223:15:touchgfx::TypedText touchgfx::TextArea::getTypedText() const	16	static
TextArea.hpp:39:7:touchgfx::TextArea::~TextArea()	16	static
TextArea.hpp:39:7:virtual touchgfx::TextArea::~TextArea()	16	static
Container.hpp:40:7:touchgfx::Container::~Container()	16	static
Container.hpp:40:7:virtual touchgfx::Container::~Container()	16	static
View.hpp:36:7:touchgfx::View<ONE_TIME_CHARGE_PROMPTPresenter>::~View()	16	static
View.hpp:36:7:virtual touchgfx::View<ONE_TIME_CHARGE_PROMPTPresenter>::~View()	16	static
ONE_TIME_CHARGE_PROMPTViewBase.hpp:19:13:ONE_TIME_CHARGE_PROMPTViewBase::~ONE_TIME_CHARGE_PROMPTViewBase()	16	static
ONE_TIME_CHARGE_PROMPTViewBase.hpp:19:13:virtual ONE_TIME_CHARGE_PROMPTViewBase::~ONE_TIME_CHARGE_PROMPTViewBase()	16	static
ONE_TIME_CHARGE_PROMPTView.hpp:11:13:ONE_TIME_CHARGE_PROMPTView::~ONE_TIME_CHARGE_PROMPTView()	16	static
ONE_TIME_CHARGE_PROMPTView.hpp:11:13:virtual ONE_TIME_CHARGE_PROMPTView::~ONE_TIME_CHARGE_PROMPTView()	16	static
ONE_TIME_CHARGE_PROMPTView.cpp:2:1:ONE_TIME_CHARGE_PROMPTView::ONE_TIME_CHARGE_PROMPTView()	16	static
ONE_TIME_CHARGE_PROMPTView.cpp:7:6:virtual void ONE_TIME_CHARGE_PROMPTView::setupScreen()	16	static
ONE_TIME_CHARGE_PROMPTView.cpp:13:6:virtual void ONE_TIME_CHARGE_PROMPTView::tearDownScreen()	16	static
Box.hpp:29:7:touchgfx::Box::~Box()	16	static
Box.hpp:29:7:virtual touchgfx::Box::~Box()	16	static
FadeAnimator.hpp:41:7:touchgfx::FadeAnimator<touchgfx::TextArea>::~FadeAnimator()	16	static
FadeAnimator.hpp:41:7:virtual touchgfx::FadeAnimator<touchgfx::TextArea>::~FadeAnimator()	16	static
Callback.hpp:357:8:touchgfx::Callback<ONE_TIME_CHARGE_PROMPTViewBase, const touchgfx::FadeAnimator<touchgfx::TextArea>&>::~Callback()	16	static
Callback.hpp:357:8:virtual touchgfx::Callback<ONE_TIME_CHARGE_PROMPTViewBase, const touchgfx::FadeAnimator<touchgfx::TextArea>&>::~Callback()	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::FadeAnimator<touchgfx::TextArea>&]	16	static
Callback.hpp:138:13:touchgfx::GenericCallback<T1, void, void>::~GenericCallback() [with T1 = const touchgfx::FadeAnimator<touchgfx::TextArea>&]	16	static
Callback.hpp:386:18:void touchgfx::Callback<dest_type, T1, void, void>::execute(T1) [with dest_type = ONE_TIME_CHARGE_PROMPTViewBase; T1 = const touchgfx::FadeAnimator<touchgfx::TextArea>&]	16	static
Callback.hpp:396:18:bool touchgfx::Callback<dest_type, T1, void, void>::isValid() const [with dest_type = ONE_TIME_CHARGE_PROMPTViewBase; T1 = const touchgfx::FadeAnimator<touchgfx::TextArea>&]	16	static
FadeAnimator.hpp:157:18:void touchgfx::FadeAnimator<T>::handleTickEvent() [with T = touchgfx::TextArea]	16	static
FadeAnimator.hpp:84:18:void touchgfx::FadeAnimator<T>::setFadeAnimationDelay(uint16_t) [with T = touchgfx::TextArea]	16	static
FadeAnimator.hpp:96:22:uint16_t touchgfx::FadeAnimator<T>::getFadeAnimationDelay() const [with T = touchgfx::TextArea]	16	static
FadeAnimator.hpp:165:10:void touchgfx::FadeAnimator<T>::nextFadeAnimationStep() [with T = touchgfx::TextArea]	32	static
