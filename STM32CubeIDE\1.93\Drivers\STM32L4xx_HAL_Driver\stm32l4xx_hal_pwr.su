stm32l4xx_hal_pwr.c:87:6:<PERSON><PERSON>_PWR_DeInit	4	static
stm32l4xx_hal_pwr.c:105:6:H<PERSON>_PWR_EnableBkUpAccess	4	static
stm32l4xx_hal_pwr.c:115:6:<PERSON><PERSON>_<PERSON>WR_DisableBkUpAccess	4	static
stm32l4xx_hal_pwr.c:312:19:HAL_PWR_ConfigPVD	16	static
stm32l4xx_hal_pwr.c:358:6:H<PERSON>_PWR_EnablePVD	4	static
stm32l4xx_hal_pwr.c:367:6:<PERSON><PERSON>_P<PERSON>_DisablePVD	4	static
stm32l4xx_hal_pwr.c:392:6:H<PERSON>_PWR_EnableWakeUpPin	16	static
stm32l4xx_hal_pwr.c:413:6:<PERSON><PERSON>_<PERSON><PERSON>_DisableWakeUpPin	16	static
stm32l4xx_hal_pwr.c:445:6:<PERSON><PERSON>_<PERSON><PERSON>_EnterSLEEPMode	16	static,ignoring_inline_asm
stm32l4xx_hal_pwr.c:524:6:H<PERSON>_PWR_EnterSTOPMode	16	static
stm32l4xx_hal_pwr.c:557:6:HAL_PWR_EnterSTANDBYMode	4	static,ignoring_inline_asm
stm32l4xx_hal_pwr.c:583:6:HAL_PWR_EnableSleepOnExit	4	static
stm32l4xx_hal_pwr.c:596:6:HAL_PWR_DisableSleepOnExit	4	static
stm32l4xx_hal_pwr.c:610:6:HAL_PWR_EnableSEVOnPend	4	static
stm32l4xx_hal_pwr.c:623:6:HAL_PWR_DisableSEVOnPend	4	static
stm32l4xx_hal_pwr.c:637:13:HAL_PWR_PVDCallback	4	static
