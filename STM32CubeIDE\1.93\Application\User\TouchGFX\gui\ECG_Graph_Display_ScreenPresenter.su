Presenter.hpp:37:18:virtual void touchgfx::Presenter::activate()	16	static
Presenter.hpp:47:18:virtual void touchgfx::Presenter::deactivate()	16	static
Presenter.hpp:52:13:touchgfx::Presenter::~Presenter()	16	static
Presenter.hpp:52:13:virtual touchgfx::Presenter::~Presenter()	16	static
Presenter.hpp:58:5:touchgfx::Presenter::Presenter()	16	static
ModelListener.hpp:21:2:ModelListener::ModelListener()	16	static
ModelListener.hpp:23:10:ModelListener::~ModelListener()	16	static
ModelListener.hpp:23:10:virtual ModelListener::~ModelListener()	16	static
ModelListener.hpp:30:15:virtual void ModelListener::Battery_Update(uint8_t)	16	static
ModelListener.hpp:31:15:virtual void ModelListener::Heart_Rate_Update(uint8_t)	16	static
ModelListener.hpp:32:15:virtual void ModelListener::SPO2_Update(uint8_t)	16	static
ModelListener.hpp:33:15:virtual void ModelListener::Temperature_Update(float)	16	static
ModelListener.hpp:34:15:virtual void ModelListener::Pedometer_Update(uint16_t)	16	static
ModelListener.hpp:35:15:virtual void ModelListener::BP_Update(uint16_t, uint16_t)	16	static
ModelListener.hpp:36:15:virtual void ModelListener::ECG_Update()	16	static
ModelListener.hpp:37:15:virtual void ModelListener::updateTime()	16	static
ModelListener.hpp:38:15:virtual void ModelListener::BLE_PASSKEY_UPDATION(uint32_t)	16	static
ModelListener.hpp:39:15:virtual void ModelListener::ERROR_POPUS(uint32_t)	16	static
ModelListener.hpp:40:15:virtual void ModelListener::UI_Update_Status(uint8_t)	16	static
ModelListener.hpp:41:15:virtual void ModelListener::Update_SSID()	16	static
ModelListener.hpp:42:15:virtual void ModelListener::UI_Up()	16	static
ModelListener.hpp:51:15:virtual void ModelListener::AlertPopup(uint8_t)	24	static
ModelListener.hpp:58:15:virtual void ModelListener::UpdateStatusBarBAT(uint8_t)	24	static
ModelListener.hpp:66:15:virtual void ModelListener::UpdateBLEStatus(wireless_status_t)	24	static
ModelListener.hpp:74:15:virtual void ModelListener::UpdateWIFIStatus(wireless_status_t)	24	static
ModelListener.hpp:82:15:virtual void ModelListener::UpdateQuickMenu(wireless_status_t)	24	static
ModelListener.hpp:114:15:virtual void ModelListener::UpdateQuickMenuBatteryPer(uint8_t)	24	static
ModelListener.hpp:123:15:virtual void ModelListener::UserInfoSuccess()	24	static
ECG_Graph_Display_ScreenPresenter.hpp:28:13:ECG_Graph_Display_ScreenPresenter::~ECG_Graph_Display_ScreenPresenter()	16	static
ECG_Graph_Display_ScreenPresenter.hpp:28:13:virtual ECG_Graph_Display_ScreenPresenter::~ECG_Graph_Display_ScreenPresenter()	16	static
ECG_Graph_Display_ScreenPresenter.cpp:5:1:ECG_Graph_Display_ScreenPresenter::ECG_Graph_Display_ScreenPresenter(ECG_Graph_Display_ScreenView&)	16	static
ECG_Graph_Display_ScreenPresenter.cpp:11:6:virtual void ECG_Graph_Display_ScreenPresenter::activate()	16	static
ECG_Graph_Display_ScreenPresenter.cpp:16:6:virtual void ECG_Graph_Display_ScreenPresenter::deactivate()	16	static
