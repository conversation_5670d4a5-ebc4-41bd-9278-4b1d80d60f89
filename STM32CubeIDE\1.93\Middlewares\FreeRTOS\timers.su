timers.c:227:12:xTimerCreateTimerTask	48	static
timers.c:282:16:xTimerCreate	40	static
timers.c:309:16:xTimerCreateStatic	48	static
timers.c:349:13:prvInitialiseNewTimer	32	static
timers.c:381:12:xTimerGenericCommand	48	static
timers.c:424:14:xTimerGetTimerDaemonTaskHandle	16	static
timers.c:433:12:xTimerGetPeriod	24	static
timers.c:442:6:vTimerSetReloadMode	24	static
timers.c:462:12:xTimerGetExpiryTime	32	static
timers.c:473:14:pcTimerGetName	24	static
timers.c:482:13:prvProcessExpiredTimer	40	static
timers.c:523:8:prvTimerTask	24	static
timers.c:559:13:prvProcessTimerOrBlockTask	24	static,ignoring_inline_asm
timers.c:619:19:prvGetNextExpireTime	24	static
timers.c:645:19:prvSampleTimeNow	24	static
timers.c:668:19:prvInsertTimerInActiveList	32	static
timers.c:709:13:prvProcessReceivedCommands	64	static
timers.c:857:13:prvSwitchTimerLists	40	static
timers.c:916:13:prvCheckForValidListAndQueue	16	static
timers.c:967:12:xTimerIsTimerActive	32	static
timers.c:992:7:pvTimerGetTimerID	32	static
timers.c:1009:6:vTimerSetTimerID	24	static
timers.c:1025:13:xTimerPendFunctionCallFromISR	48	static
timers.c:1049:13:xTimerPendFunctionCall	48	static
timers.c:1078:14:uxTimerGetTimerNumber	16	static
timers.c:1088:7:vTimerSetTimerNumber	16	static
