# AWT Communication Library (AWT_COMM/Lib) Documentation

## Overview

The AWT Communication Library is a comprehensive, layered communication stack designed for wireless data transmission in the AWT wearable device. It implements a robust protocol with state machines, queuing systems, and error handling for reliable BLE/WiFi communication.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    Application Layer                            │
│  (SPO2, Heart Rate, ECG, etc. - Data Sources)                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                Communication Manager                            │
│  • Com_Send_Data() • Com_Get_Data() • Com_BufferXfer()        │
│  • Connection Management • Queue Coordination                  │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 Primitive Manager                              │
│  • Command/Response Handling • Indication Processing          │
│  • State Machine • Retry Logic • Timeout Management          │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                Transport Manager                               │
│  • Packet Formatting • CRC Calculation • Buffer Transfer      │
│  • Master/Slave Protocol • ACK/NACK Handling                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 Serial Interface                               │
│  • UART3 (BLE) • UART5 (WiFi) • DMA Management               │
│  • Platform Abstraction • Hardware Control                    │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Communication Manager (`Comm_Manager.c`)

**Purpose**: Top-level API and coordination layer

#### Key Functions:
```c
// Send data via wireless communication
int32_t Com_Send_Data(enum comm_subid subid, uint8_t* data, uint16_t len, com_callback callback);

// Request data from remote device
int32_t Com_Get_Data(enum comm_subid subid, com_callback callback);

// Transfer large data buffers
int32_t Com_BufferXfer(enum comm_subid subid, uint8_t* data, uint16_t len, com_callback callback);
```

#### Communication Queue System:
```c
typedef struct Comm_Queue {
    enum com_module module;        // ul, ul_rt, m_isr, m_wireless
    union {
        pe_trigger pt;             // Primitive trigger
        tt_trigger tt;             // Transport trigger
        w_trigger_t wt;            // Wireless trigger
    } event;
} Comm_Queue_t;
```

#### Module Types:
- **`ul`** (Upper Layer): Normal application requests
- **`ul_rt`** (Upper Layer Real-time): Time-critical requests
- **`m_isr`** (ISR): Interrupt service routine calls
- **`m_wireless`** (Wireless): Wireless module events

### 2. Primitive Manager (`PrimitiveManager.c`)

**Purpose**: Protocol state machine and message handling

#### State Machines:

##### Command/Response State Machine:
```c
enum p_states {
    p_cr_idle,          // Idle state
    p_csend,            // Command send
    p_csuccess,         // Command send success
    p_rrecv,            // Response received
    p_rexec,            // Response executed
    p_crecv,            // Command receive
    p_cexec,            // Command execute
    p_rsend,            // Response sent
    p_cr_tout,          // Timeout
    p_cr_msgfail,       // Message fail
    p_cr_transpfail,    // Transport fail
    p_cr_invalidstate   // Invalid state
};
```

##### Indication State Machine:
```c
enum p_states {
    p_ind_idle,         // Idle state
    p_isend,            // Indication send
    p_irecv,            // Indication receive
    p_iexec,            // Indication executed
    p_bufferxfer,       // Buffer transfer
    p_bufferxfer_rx,    // Buffer transfer receive
    p_exec_bufferxfer,  // Execute buffer transfer
    p_ind_touts,        // Indication timeout
    p_ind_msgfail,      // Message fail
    p_ind_transpfail    // Transport fail
};
```

#### Trigger Types:
```c
enum _ptrigger {
    cmd_tx,             // Transmit command
    cmd_f,              // Command fail
    cmd_s,              // Command success
    resp_r,             // Response received
    cr_ack_s,           // ACK sent
    cr_nack_s,          // NACK sent
    cr_exec_s,          // Execute success
    cr_exec_f,          // Execute fail
    cmd_r,              // Command received
    rsp_s,              // Response sent success
    rsp_f,              // Response send fail
    ind_tx,             // Transmit indication
    ind_r,              // Receive indication
    ind_s,              // Indication success
    ind_f,              // Indication fail
    buff_tx,            // Buffer transfer
    // ... more triggers
};
```

### 3. Transport Manager (`TransportManager.c`)

**Purpose**: Low-level packet handling and protocol implementation

#### State Machine:
```c
enum t_states {
    t_close,            // Closed state
    t_idle,             // Idle state
    master_init,        // Master initialization
    m_tx,               // Master transmit
    m_rx,               // Master receive
    m_proc,             // Master process
    slave_init,         // Slave initialization
    s_rxh,              // Slave receive header
    s_rxp,              // Slave receive payload
    s_proc,             // Slave process
    s_tx,               // Slave transmit
    t_hclose,           // Half close
    t_timeouts,         // Timeout state
    t_invalidstate      // Invalid state
};
```

#### Packet Structure:
```c
#pragma pack(1)
struct _Exchange {
    uint8_t  Header;        // Protocol header (0xAA)
    uint8_t  ID;            // Message type (COMMAND, RESPONSE, INDICATION, BUFFERTX)
    uint16_t SUB_ID;        // Sub-identifier (comm_subid)
    uint16_t size;          // Payload size
    uint8_t  Data[MAX_DATA]; // Payload data
};

struct footer {
    uint32_t crc;           // CRC32 checksum
    uint8_t footer;         // Protocol footer (0x55)
};
```

#### Buffer Transfer System:
```c
struct Buffer_Xfer {
    uint8_t *payload;       // Data pointer
    uint8_t current_segment; // Current segment number
    uint8_t max_segments;   // Total segments
    uint16_t mtu;           // Maximum transmission unit
    uint32_t tot_len;       // Total length
    uint32_t timestamp;     // Timestamp
    uint8_t requester;      // Requester ID
    uint32_t crc;           // CRC checksum
};
```

### 4. Queue Management

#### Primitive Queues (`PrimitiveQueue.c`):
- **Command/Response Queue**: For request-response transactions
- **Indication Queue**: For one-way data transmission
- **Priority Queues**: Front-insertion for high-priority messages

#### Transport Queue (`TransportQueue.c`):
- **Transport Trigger Queue**: For transport layer events
- **Circular Buffer**: FIFO implementation with overflow protection

### 5. Serial Interface (`SerialInterface.c`)

**Purpose**: Hardware abstraction and UART management

#### Radio Types:
```c
enum radioType {
    Radio_None,         // No radio selected
    Radio_BLE,          // BLE module (UART3)
    Radio_WiFi          // WiFi module (UART5)
};
```

#### Key Functions:
```c
// Platform-specific transmission
int _platform_transmit(uint8_t *data, uint16_t len);

// UART configuration
void Open_Master_rPort();   // Master mode with RX/TX
void Open_Slave_Port();     // Slave mode
void Close_Port();          // Close communication

// Wireless module control
void switchOnBle(void);
void switchOffBle(void);
void wakeupWireless(uint8_t action);
```

## Protocol Specifications

### Message Types:
```c
enum _Exchange_Type {
    COMMAND,            // Request-response transaction
    RESPONSE,           // Response to command
    INDICATION,         // One-way data transmission
    BUFFERTX,           // Large data transfer
    comACK              // Acknowledgment
};
```

### Communication Sub-IDs:
```c
enum comm_subid {
    comm_wHR           = 0x0029,    // Heart rate
    comm_wSPO2         = 0x002A,    // SPO2 processed
    comm_wCBT          = 0x002B,    // Core body temperature
    comm_wBP           = 0x002D,    // Blood pressure
    comm_wECG          = 0x002F,    // ECG data
    comm_wSpo2rawData  = 0x0032,    // SPO2 raw data
    // ... more sub-IDs
};
```

### Packet Format:
```
┌─────────┬─────┬─────────┬──────────┬─────────────────┬─────────┬─────────┐
│ Header  │ ID  │ SUB_ID  │   Size   │     Payload     │   CRC   │ Footer  │
│ (1 byte)│(1 B)│ (2 B)   │ (2 bytes)│   (Variable)    │(4 bytes)│ (1 byte)│
│  0xAA   │     │         │          │                 │         │  0x55   │
└─────────┴─────┴─────────┴──────────┴─────────────────┴─────────┴─────────┘
```

## Data Flow Examples

### 1. SPO2 Data Transmission:
```c
// Application calls
Com_Send_Data(comm_wSpo2rawData, spo2_data, data_len, NULL);

// Flow:
// 1. Communication Manager → Queue message
// 2. Primitive Manager → Process indication
// 3. Transport Manager → Format packet, add CRC
// 4. Serial Interface → Transmit via UART3 (BLE)
```

### 2. Large Data Buffer Transfer:
```c
// Application calls
Com_BufferXfer(comm_wEcgrawData, ecg_buffer, buffer_size, callback);

// Flow:
// 1. Split data into MTU-sized segments
// 2. Send each segment with sequence number
// 3. Wait for ACK before sending next segment
// 4. Handle retransmission on NACK
```

## Error Handling

### Retry Mechanism:
- **Default Retries**: 3 attempts per message
- **Timeout Handling**: Configurable timeouts per layer
- **Exponential Backoff**: Increasing delays between retries

### Error Types:
```c
typedef enum {
    com_success,        // Operation successful
    com_fail,           // Operation failed
    com_timeout,        // Operation timed out
    com_invalid,        // Invalid parameters
    com_busy,           // System busy
    com_disconnected    // Connection lost
} com_result;
```

### Connection Management:
```c
// Check connection status
int32_t isCommConnected(void);

// Connection states
typedef enum {
    ble_off,            // BLE disabled
    ble_on,             // BLE enabled
    ble_connected,      // BLE connected
    wifi_connected,     // WiFi connected
    comm_disconnected   // Disconnected
} comm_state_t;
```

## Configuration and Debugging

### Debug Configuration:
```c
#define COMM_MANAGER_DEBUG      1   // Enable communication manager debug
#define TRANSPORT_MANAGER_DEBUG 1   // Enable transport manager debug
#define PRIMITIVE_MANAGER_DEBUG 1   // Enable primitive manager debug
```

### Memory Configuration:
```c
#define MAX_DATA               (1024 * 3)  // Maximum payload size
#define PRIMITIVE_QUEUE_SIZE   10          // Primitive queue capacity
#define TRANSPORT_QUEUE_SIZE   4           // Transport queue capacity
```

### Performance Tuning:
```c
#define MTU_SIZE_BLE          244          // BLE MTU size
#define MTU_SIZE_WIFI         1024         // WiFi MTU size
#define DEFAULT_TIMEOUT       5000         // Default timeout (ms)
#define RETRY_COUNT           3            // Default retry count
```

## Detailed Implementation Analysis

### State Machine Interactions

#### Communication Flow State Transitions:
```
Application Request → Communication Manager → Primitive Manager → Transport Manager → Serial Interface

1. Com_Send_Data() called
2. Message queued in Communication Manager
3. Primitive Manager processes indication (ind_tx)
4. State: p_ind_idle → p_isend
5. Transport Manager formats packet
6. State: t_idle → master_init → m_tx
7. Serial Interface transmits via UART
8. Wait for ACK/NACK response
9. State: m_tx → m_rx → m_proc → t_idle
10. Callback notification to application
```

#### Master-Slave Protocol:
```
Master Device (AWT Watch)          Slave Device (BLE/WiFi Module)
     │                                        │
     ├─── Send Packet ──────────────────────→ │
     │                                        ├─── Process Packet
     │                                        ├─── Send ACK/NACK
     │ ←──────────────────── ACK/NACK ─────── │
     ├─── Process Response                    │
     │                                        │
```

### Memory Management

#### Buffer Allocation:
```c
// Static memory allocation for communication
static struct _Exchange packet_buffer;          // ~3KB
static struct Buffer_Xfer bufferxfer_context;   // ~20 bytes
static pe_trigger primitive_queues[20];         // ~400 bytes
static tt_trigger transport_queue[4];           // ~16 bytes

// Total static memory: ~3.5KB
```

#### Dynamic Memory Usage:
- **Payload Pointers**: Application data referenced, not copied
- **Queue Management**: Fixed-size circular buffers
- **No malloc/free**: All memory statically allocated

### Threading and Synchronization

#### FreeRTOS Integration:
```c
// Communication task priority and stack
#define COMM_TASK_PRIORITY    (tskIDLE_PRIORITY + 2)
#define COMM_TASK_STACK_SIZE  (1024 * 2)

// Queue handles
QueueHandle_t Comm_Queue_Handle;
SemaphoreHandle_t Comm_Semaphore;

// Task notification for time-critical operations
xTaskNotifyWait(0x0, 0xffffffff, &notification, timeout);
```

#### Thread Safety:
- **ISR-Safe Functions**: Separate ISR variants for interrupt context
- **Atomic Operations**: Critical sections protected
- **Queue Synchronization**: FreeRTOS queues for thread communication

### Performance Characteristics

#### Throughput Analysis:
```
BLE (UART3 @ 230400 baud):
- Theoretical: 28.8 KB/s
- Practical: ~20 KB/s (with protocol overhead)
- Latency: 10-50ms per packet

WiFi (UART5 @ 230400 baud):
- Theoretical: 28.8 KB/s
- Practical: ~25 KB/s (lower protocol overhead)
- Latency: 5-20ms per packet
```

#### Protocol Overhead:
```
Packet Overhead:
- Header: 6 bytes (Header + ID + SUB_ID + Size)
- Footer: 5 bytes (CRC + Footer)
- Total: 11 bytes per packet
- Efficiency: (Payload / (Payload + 11)) * 100%

Buffer Transfer Overhead:
- Additional: 15 bytes per segment
- Segmentation efficiency depends on MTU size
```

### Advanced Features

#### Buffer Transfer Protocol:
```c
// Large data transfer implementation
typedef struct {
    uint8_t segment_number;     // Current segment (0-based)
    uint16_t mtu_size;          // Maximum transmission unit
    uint32_t total_length;      // Total data length
    uint32_t timestamp;         // Transfer timestamp
    uint8_t data_origin;        // Requester identification
    uint32_t segment_crc;       // Per-segment CRC
} buffer_segment_header_t;

// Transfer process:
// 1. Calculate total segments needed
// 2. Send segments sequentially
// 3. Wait for ACK before next segment
// 4. Handle retransmission on NACK
// 5. Final confirmation when complete
```

#### CRC Calculation:
```c
// CRC32 implementation for data integrity
uint32_t get_crc(uint8_t *data, uint32_t len) {
    // Polynomial: 0x04C11DB7 (IEEE 802.3)
    // Initial value: 0xFFFFFFFF
    // Final XOR: 0xFFFFFFFF
    // Covers: ID + SUB_ID + Size + Payload
}
```

#### Connection State Management:
```c
typedef enum {
    COMM_STATE_DISCONNECTED,    // No connection
    COMM_STATE_CONNECTING,      // Connection in progress
    COMM_STATE_CONNECTED,       // Active connection
    COMM_STATE_ERROR,           // Connection error
    COMM_STATE_TIMEOUT          // Connection timeout
} comm_connection_state_t;

// Connection dependency checking
int32_t isNwDependentSubid(enum comm_subid subid) {
    // Returns 1 if subid requires active connection
    // Returns 0 if subid can be sent without connection
}
```

### Debugging and Diagnostics

#### Debug Print Macros:
```c
#ifdef DEBUG_PRINTS
#define COM_PRINTI(fmt, ...) printf("[COMM] " fmt, ##__VA_ARGS__)
#define COM_PRINTD(fmt, ...) printf("[DEBUG] " fmt, ##__VA_ARGS__)
#else
#define COM_PRINTI(fmt, ...)
#define COM_PRINTD(fmt, ...)
#endif
```

#### Diagnostic Functions:
```c
// State inspection
uint8_t CurState_CR(void);          // Current command/response state
uint8_t CurState_I(void);           // Current indication state
int GetTransportManagerState(void); // Transport manager state

// Queue status
int isCmdTxEmpty(void);             // Command queue empty
int isIndTxFull(void);              // Indication queue full
int isTriggerEmpty(void);           // Transport queue empty

// Connection status
int32_t isCommConnected(void);      // Connection status
int32_t isCommDisconnected(void);   // Disconnection status
```

#### Error Logging:
```c
typedef struct {
    uint32_t timestamp;
    enum comm_subid subid;
    enum _ptrigger trigger;
    uint8_t error_code;
    uint8_t retry_count;
} comm_error_log_t;

// Error tracking for debugging
static comm_error_log_t error_log[10];
static uint8_t error_log_index = 0;
```

### Integration Guidelines

#### Application Integration:
```c
// 1. Initialize communication system
void Comm_Init(void) {
    InitQueue();                    // Initialize primitive queues
    InitTriggerQueue();            // Initialize transport queue
    Init_Wireless(NULL);           // Initialize wireless interface
}

// 2. Send data example
int send_sensor_data(uint8_t *data, uint16_t len) {
    // Check connection
    if (!isCommConnected()) {
        return -1;
    }

    // Send data
    return Com_Send_Data(comm_wSpo2rawData, data, len, sensor_callback);
}

// 3. Callback handling
void sensor_callback(com_result result) {
    if (result == com_success) {
        // Data sent successfully
        update_transmission_status(SUCCESS);
    } else {
        // Handle transmission failure
        handle_transmission_error(result);
    }
}
```

#### Best Practices:
1. **Always check connection status** before sending data
2. **Use appropriate sub-IDs** for different data types
3. **Implement proper callbacks** for asynchronous operations
4. **Handle retry logic** in application layer if needed
5. **Monitor queue status** to prevent overflow
6. **Use buffer transfer** for large data (>1KB)

### Troubleshooting Guide

#### Common Issues:

1. **Data Not Transmitting**:
   ```c
   // Check connection
   if (!isCommConnected()) {
       // BLE/WiFi not connected
   }

   // Check queue status
   if (isIndTxFull()) {
       // Queue overflow - wait or increase queue size
   }
   ```

2. **Transmission Timeouts**:
   ```c
   // Increase timeout values
   #define PRIMITIVE_TIMEOUT  10000  // 10 seconds
   #define TRANSPORT_TIMEOUT  5000   // 5 seconds
   ```

3. **CRC Errors**:
   ```c
   // Check data integrity
   uint32_t calc_crc = get_crc(data, len);
   // Verify UART configuration and signal integrity
   ```

4. **Memory Issues**:
   ```c
   // Monitor stack usage
   #define COMM_TASK_STACK_SIZE  (1024 * 4)  // Increase if needed

   // Check queue sizes
   #define PRIMITIVE_QUEUE_SIZE  20  // Increase if overflow
   ```

## Retry Mechanisms in AWT Communication Library

### Overview

The AWT Communication Library implements a comprehensive retry system across multiple layers to ensure reliable data transmission. The retry mechanisms handle various failure scenarios including timeouts, transport failures, and communication errors.

### Retry Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Application Layer                            │
│                 (Initial Request)                               │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                Communication Manager                            │
│  • Default Retry Count: 3                                     │
│  • Connection Validation                                       │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 Primitive Manager                              │
│  • Timeout Retry: Command/Response & Indication               │
│  • Transport Failure Retry                                    │
│  • State-based Retry Logic                                    │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                Transport Manager                               │
│  • CRC Error Handling                                         │
│  • ACK/NACK Processing                                        │
│  • Port Reset on Failure                                      │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 Serial Interface                               │
│  • Hardware-level Error Recovery                              │
│  • Port Reset and Reinitialization                           │
└─────────────────────────────────────────────────────────────────┘
```

### Retry Configuration

#### Default Retry Settings:
```c
#define MAX_MASTER_RETRIES     5        // Maximum retries at transport level
#define CRTIME                 60000    // Command/Response timeout (60 seconds)
#define ITIME                  30000    // Indication timeout (30 seconds)
#define TrFailTimeout          10       // Transport failure timeout (10ms)

// Default retry counts set in Communication Manager
msg.event.pt.retry = 3;                 // Standard retry count
```

#### Retry Structure:
```c
typedef struct primitive_trigger {
    enum comm_subid sub_id;              // Communication sub-identifier
    enum _ptrigger trigger;              // Trigger type
    void *payload;                       // Data payload
    uint16_t len;                        // Data length
    uint8_t retry;                       // Retry counter
    com_callback function;               // Completion callback
} pe_trigger;
```

### Retry Types and Mechanisms

#### 1. **Timeout-Based Retry**

##### Command/Response Timeout Retry:
```c
static int handle_p_cr_timeout() {
    if(crTrigger.retry > 0) {
        crTrigger.trigger = cmd_tx;      // Reset to command transmit
        crTrigger.payload = 0;           // Clear payload
        crTrigger.len = 0;               // Clear length
        crTrigger.retry--;               // Decrement retry counter
        triggerFromPrimitive(&crTrigger); // Re-queue for retry
        crTrigger.trigger = p_invalid;   // Reset trigger

        if(curState_I == p_ind_idle) {
            SetTransportTrigger(t_timeout); // Trigger transport timeout
        } else {
            pendTimeout = 1;             // Pend timeout for later
        }
    } else if (crTrigger.retry == 0) {
        return p_cr_changeState(p_cr_msgfail); // Final failure
    }
    return p_cr_changeState(p_cr_idle);  // Return to idle
}
```

##### Indication Timeout Retry:
```c
static int handle_p_ind_timeout() {
    if(iTrigger.retry > 0) {
        iTrigger.retry--;                // Decrement retry counter
        triggerFromPrimitive(&iTrigger); // Re-queue for retry
        iTrigger.trigger = p_invalid;    // Reset trigger
        SetTransportTrigger(t_timeout);  // Trigger transport timeout
    } else if (iTrigger.retry == 0) {
        return p_ind_changeState(p_ind_msgfail); // Final failure
    }
    return p_ind_changeState(p_ind_idle); // Return to idle
}
```

#### 2. **Transport Failure Retry**

##### Command/Response Transport Failure:
```c
static int handle_p_cr_transpfail() {
    COM_PRINTI("In handle_p_cr_transpfail retry count %d state[%d]\n",
               crTrigger.retry, prevState_CR);

    if(crTrigger.retry > 0) {
        crTrigger.retry--;               // Decrement retry counter

        switch(prevState_CR) {
        case p_csend: {
            crTrigger.trigger = cmd_tx;  // Reset to command transmit
            crTrigger.payload = 0;       // Clear payload
            crTrigger.len = 0;           // Clear length
            enqueueCmdTxToFront(&crTrigger); // Priority re-queue
            crTrigger.trigger = p_invalid;   // Reset trigger
        } break;

        case p_rsend: {
            // Response send retry logic
        } break;
        }
        return p_cr_changeState(prevState_CR); // Return to previous state
    } else {
        NotifycrTrFail();                // Notify transport failure
        return p_cr_changeState(p_cr_idle); // Return to idle
    }
}
```

##### Indication Transport Failure:
```c
static int handle_p_ind_transpfail() {
    COM_PRINTI("In handle_p_ind_transpfail retry count %d\n", iTrigger.retry);

    if(iTrigger.retry > 0) {
        iTrigger.trigger = ind_tx;       // Reset to indication transmit
        iTrigger.retry--;                // Decrement retry counter
        enqueueIndTxToFront(&iTrigger);  // Priority re-queue
        iTrigger.trigger = p_invalid;    // Reset trigger
        return p_ind_changeState(p_isend); // Return to send state
    }

    NotifyiTrFail();                     // Notify transport failure
    return p_ind_changeState(p_ind_idle); // Return to idle
}
```

#### 3. **Priority Queue Retry**

The system implements priority queues for retry operations:

```c
// Front-insertion for high-priority retries
int enqueueCmdTxToFront(pe_trigger* item);
int enqueueIndTxToFront(pe_trigger* item);

// Update retry trigger with priority queuing
int32_t UpdatePrimitiveTriggerRetry(pe_trigger* trigger) {
    if(IsCR(trigger->trigger)) {
        COM_PRINTI("UpdateTriggerRetry -->CR %d %d %p %d\r\n",
                   trigger->trigger, trigger->sub_id, trigger->payload, trigger->len);
        return enqueueCmdTxToFront(trigger); // Priority queue for commands
    }
    if(IsInd(trigger->trigger)) {
        COM_PRINTI("UpdateTriggerRetry -->Ind %d %d %p %d\r\n",
                   trigger->trigger, trigger->sub_id, trigger->payload, trigger->len);
        return enqueueIndTxToFront(trigger); // Priority queue for indications
    }
    return 0;
}
```

### Timer-Based Retry System

#### Timer Configuration:
```c
// Timer callback for Command/Response timeout
void crTimeoutCallback(struct tmrTimerControl * _timer) {
    Comm_Queue_t queue;
    queue.module = ul_rt;                // Real-time module
    queue.event.pt.trigger = p_cr_timeout; // CR timeout trigger
    queue.event.pt.len = 0;
    queue.event.pt.payload = 0;
    queue.event.pt.sub_id = comm_NONE;
    queue.event.pt.function = 0;
    triggerCommMngr(&queue);             // Trigger communication manager
}

// Timer callback for Indication timeout
void iTimeoutCallback(struct tmrTimerControl * _timer) {
    Comm_Queue_t queue;
    queue.module = ul_rt;                // Real-time module
    queue.event.pt.trigger = p_ind_timeout; // Indication timeout trigger
    queue.event.pt.len = 0;
    queue.event.pt.payload = 0;
    queue.event.pt.sub_id = comm_NONE;
    queue.event.pt.function = 0;
    triggerCommMngr(&queue);             // Trigger communication manager
}
```

#### Timer Management:
```c
// Start/stop timers for different message types
int32_t resetPrimitiveMaster(enum _Exchange_Type primitive) {
    if(primitive == COMMAND) {
        stopCRTimer();                   // Stop command/response timer
        startCRTimer(TrFailTimeout);     // Start with transport failure timeout
    } else if (primitive == INDICATION) {
        stopITimer();                    // Stop indication timer
        startITimer(TrFailTimeout);      // Start with transport failure timeout
    }
    return 0;
}
```

### Transport Layer Retry

#### Break Event Handling:
```c
static int handle_break_event(void) {
    if(_platform_break_handle() == STATE_CHANGE) {
#ifdef TRANSPORT_FAIL
        if(IsTransportMaster()) {        // If we're the master
            enum _ptrigger ptrigger = p_invalid;
            switch(ID) {
            case BUFFERTX:
            case INDICATION:
                ptrigger = ind_transp_f; // Indication transport failure
                break;
            case RESPONSE:
            case COMMAND:
                ptrigger = cr_transp_f;  // Command/response transport failure
                break;
            }

            COM_PRINTI("Port closed by slave trigger[%d] PM sent transport failure", ptrigger);
            SetPrimitiveTrigger_Data(ptrigger, NULL, 0, comm_NONE);
        }
#endif
        return t_changeState(t_close);   // Close transport
    }
    return STATE_WAIT;
}
```

#### Timeout Event Handling:
```c
static int handle_timeout_event() {
    switch(currentState) {
    case slave_init:
    case s_rxh:
    case s_rxp:
    case s_proc:
    case s_tx: {
        pending_timeout = 1;             // Pend timeout for slave states
        return STATE_WAIT;
    }
    case t_idle:
    case master_init:
    case m_tx:
    case m_rx:
    case m_proc: {
        return t_changeState(t_timeouts); // Go to timeout state
    }
    default:
        return STATE_WAIT;
    }
}
```

### Error Recovery Mechanisms

#### CRC Error Recovery:
```c
static int32_t Check_CommCrc(struct _Exchange *packet) {
    if(packet->size == 0) {
        return 1;                        // No payload, CRC OK
    } else {
        uint32_t calc_crc = (uint32_t)(packet->Data[packet->size] |
                                      packet->Data[packet->size+1]<<8 |
                                      packet->Data[packet->size+2]<<16 |
                                      packet->Data[packet->size+3]<<24);
        return (get_crc(&packet->ID, (uint32_t)(packet->size + HDR_SIZE -1)) == calc_crc);
    }
    return 0;
}

// CRC failure triggers transport failure and retry
if(!(CHECK_FTR(ipacket) && Check_CommCrc(&ipacket) && (ipacket.ID <= BUFFERTX))) {
#ifdef TRANSPORT_FAIL
    TIMEOUT_STATE_MACHINE();            // Trigger timeout state machine
#endif
}
```

#### Port Reset Recovery:
```c
static int handle_timeout() {
    COM_PRINTI("In handle_timeout state \r\n");
    Reset_Port();                        // Reset the communication port
    return STATE_WAIT;
}
```

### Callback Notification System

#### Success/Failure Callbacks:
```c
static void HandleAppCallback() {
    com_result value;
    com_callback callback = 0;

    switch(currentEvent) {
    case rsp_s:
        value = com_success;             // Response success
        callback = crTrigger.function;
        break;
    case rsp_f:
        value = com_fail;                // Response failure
        callback = crTrigger.function;
        break;
    case ind_s:
        value = com_success;             // Indication success
        callback = iTrigger.function;
        break;
    case ind_f:
        value = com_fail;                // Indication failure
        callback = iTrigger.function;
        break;
    }

    if(callback != 0)
        (*callback)(value);              // Execute application callback
}
```

This comprehensive documentation covers all aspects of the AWT Communication Library implementation, from high-level architecture to low-level implementation details, including the sophisticated retry mechanisms that ensure reliable communication.
