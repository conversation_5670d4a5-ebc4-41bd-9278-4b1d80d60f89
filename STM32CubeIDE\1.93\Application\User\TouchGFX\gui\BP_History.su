Drawable.hpp:62:13:touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:62:13:virtual touchgfx::Drawable::~Drawable()	16	static
Drawable.hpp:132:23:virtual touchgfx::Drawable* touchgfx::Drawable::getFirstChild()	16	static
Drawable.hpp:269:18:virtual void touchgfx::Drawable::setX(int16_t)	16	static
Drawable.hpp:281:18:virtual void touchgfx::Drawable::setY(int16_t)	16	static
Drawable.hpp:297:10:void touchgfx::Drawable::setXY(int16_t, int16_t)	16	static
Drawable.hpp:310:18:virtual void touchgfx::Drawable::setWidth(int16_t)	16	static
Drawable.hpp:322:18:virtual void touchgfx::Drawable::setHeight(int16_t)	16	static
Drawable.hpp:332:18:virtual void touchgfx::Drawable::childGeometryChanged()	16	static
Drawable.hpp:343:18:virtual void touchgfx::Drawable::handleClickEvent(const touchgfx::ClickEvent&)	16	static
Drawable.hpp:354:18:virtual void touchgfx::Drawable::handleGestureEvent(const touchgfx::GestureEvent&)	16	static
Drawable.hpp:442:18:virtual void touchgfx::Drawable::handleDragEvent(const touchgfx::DragEvent&)	16	static
Drawable.hpp:452:18:virtual void touchgfx::Drawable::handleTickEvent()	16	static
Drawable.hpp:466:10:void touchgfx::Drawable::setVisible(bool)	16	static
Drawable.hpp:488:10:bool touchgfx::Drawable::isVisible() const	16	static
Drawable.hpp:500:10:bool touchgfx::Drawable::isTouchable() const	16	static
Drawable.hpp:539:18:virtual void touchgfx::Drawable::moveTo(int16_t, int16_t)	16	static
Drawable.hpp:576:10:void touchgfx::Drawable::resetDrawChainCache()	16	static
Drawable.hpp:654:18:virtual void touchgfx::Drawable::setupDrawChain(const touchgfx::Rect&, touchgfx::Drawable**)	24	static
Container.hpp:152:23:virtual touchgfx::Drawable* touchgfx::Container::getFirstChild()	16	static
TypedText.hpp:65:13:touchgfx::TypedText::~TypedText()	16	static
TypedText.hpp:65:13:virtual touchgfx::TypedText::~TypedText()	16	static
Widget.hpp:47:18:virtual void touchgfx::Widget::getLastChild(int16_t, int16_t, touchgfx::Drawable**)	24	static
Widget.hpp:36:7:touchgfx::Widget::~Widget()	16	static
Widget.hpp:36:7:virtual touchgfx::Widget::~Widget()	16	static
TextArea.hpp:39:7:touchgfx::TextArea::~TextArea()	16	static
TextArea.hpp:39:7:virtual touchgfx::TextArea::~TextArea()	16	static
Container.hpp:40:7:touchgfx::Container::~Container()	16	static
Container.hpp:40:7:virtual touchgfx::Container::~Container()	16	static
BP_HistoryBase.hpp:17:13:BP_HistoryBase::~BP_HistoryBase()	16	static
BP_HistoryBase.hpp:17:13:virtual BP_HistoryBase::~BP_HistoryBase()	16	static
BP_History.hpp:11:10:BP_History::~BP_History()	16	static
BP_History.hpp:11:10:virtual BP_History::~BP_History()	16	static
BP_History.cpp:5:1:BP_History::BP_History()	16	static
BP_History.cpp:11:6:virtual void BP_History::initialize()	24	static
BP_History.cpp:41:6:void BP_History::Month(uint8_t, touchgfx::Unicode::UnicodeChar*)	24	static
BP_History.cpp:85:6:virtual void BP_History::handleTickEvent()	16	static
BP_History.cpp:89:6:void BP_History::Container1()	40	static
BP_History.cpp:214:6:void BP_History::Container2()	40	static
BP_History.cpp:338:6:void BP_History::Container3()	40	static
BP_History.cpp:462:6:void BP_History::Container4()	40	static
BP_History.cpp:585:6:void BP_History::Container5()	40	static
BP_History.cpp:708:6:void BP_History::Container6()	40	static
BP_History.cpp:832:6:void BP_History::Container7()	40	static
BP_History.cpp:945:6:void BP_History::Container8()	40	static
BP_History.cpp:1071:6:void BP_History::Container9()	40	static
BP_History.cpp:1195:6:void BP_History::Container10()	40	static
BP_History.cpp:1320:6:void BP_History::Container11()	40	static
BP_History.cpp:1443:6:void BP_History::Container12()	40	static
BP_History.cpp:1568:6:void BP_History::AMPM()	16	static
TextAreaWithWildcard.hpp:90:7:touchgfx::TextAreaWithTwoWildcards::~TextAreaWithTwoWildcards()	16	static
TextAreaWithWildcard.hpp:90:7:virtual touchgfx::TextAreaWithTwoWildcards::~TextAreaWithTwoWildcards()	16	static
TextAreaWithWildcard.hpp:34:7:touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
TextAreaWithWildcard.hpp:34:7:virtual touchgfx::TextAreaWithOneWildcard::~TextAreaWithOneWildcard()	16	static
Image.hpp:38:7:touchgfx::Image::~Image()	16	static
Image.hpp:38:7:virtual touchgfx::Image::~Image()	16	static
